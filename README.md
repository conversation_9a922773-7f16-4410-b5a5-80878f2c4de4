# OTA订单处理系统

一个智能化的OTA（Online Travel Agency）订单处理系统，支持文字和图片输入，使用AI自动处理订单信息，并通过API创建订单。

## 功能特性

### 🔐 用户认证
- 安全的登录系统
- 持久化登录状态
- 自动会话管理

### 📝 订单输入
- **文字输入**：支持直接输入订单文本
- **图片上传**：支持拖拽上传多张图片
- **OCR识别**：自动从图片中提取订单信息

### 🤖 AI智能处理
- **多AI架构**：Google Vision + DeepSeek/Gemini双LLM系统
- **图像分析**：Google Vision API进行OCR文字提取和图像理解
- **文字处理**：DeepSeek主要LLM，Gemini备用LLM，15秒超时自动切换
- **状态指示器**：实时显示所有AI服务连接状态
- **智能选择**：基于订单内容自动选择车型、用户、分类
- 自动识别OTA类型
- 智能日期修正和时间计算
- 酒店名称标准化
- 自动生成参考号码
- **举牌服务识别**：自动检测订单中的举牌服务需求
- **智能订单分离**：自动为接机订单生成独立的举牌服务订单

### 📋 结果预览与编辑
- 实时预览处理结果
- 支持手动编辑修正
- 多订单批量显示
- 数据验证和格式化

### 🚀 订单创建
- 自动调用GoMyHire API
- 批量创建订单
- 实时状态反馈
- 错误处理和重试机制

## 技术栈

- **前端**：HTML5, CSS3, JavaScript (ES6+)
- **UI框架**：原生CSS + 现代化设计
- **AI服务**：
  - Google Vision API（图像分析和OCR）
  - DeepSeek API（主要文字处理LLM）
  - Gemini API（备用文字处理LLM）
- **API集成**：GoMyHire REST API
- **存储**：LocalStorage
- **架构**：纯前端单页应用，模块化设计

## 快速开始

### 1. 环境准备

确保您有以下环境：
- 现代浏览器（Chrome, Firefox, Safari, Edge）
- 网络连接（用于API调用）

### 2. 获取API密钥

#### Gemini AI API Key
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录您的Google账户
3. 创建新的API密钥
4. 复制API密钥

### 3. 配置系统

1. 打开 `app.js` 文件
2. 找到配置部分：
```javascript
const CONFIG = {
    // ...
    GEMINI_API_KEY: 'AIzaSyDXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', // 替换为您的API Key
    // ...
};
```
3. 将 `GEMINI_API_KEY` 替换为您的实际API密钥

### 4. 启动系统

1. 将所有文件放在同一目录下
2. 使用Web服务器打开 `index.html`
   - **方法1**：使用Python简单服务器
     ```bash
     python -m http.server 8000
     ```
   - **方法2**：使用Node.js服务器
     ```bash
     npx http-server
     ```
   - **方法3**：使用VS Code Live Server扩展

3. 在浏览器中访问 `http://localhost:8000`

## 使用指南

### 登录系统

1. 打开系统后会自动弹出登录窗口
2. 默认登录信息：
   - 邮箱：`<EMAIL>`
   - 密码：`1234`
3. 点击"登录"按钮

### 输入订单信息

#### 文字输入
1. 选择"文字输入"标签
2. 在文本框中输入订单信息，例如：
```
1.28接机：KE671 22.20抵达
1.30送机：AK378 16.20起飞

联系人：张梦媛
人数：6
车型：商务十座
酒店：Santa Grand Signature

JY
```

#### 图片上传
1. 选择"图片上传"标签
2. 点击上传区域或拖拽图片文件
3. 支持多张图片同时上传
4. 系统会自动提取图片中的文字信息

### 处理订单

1. 选择OTA类型（默认为"自动识别"）
2. 点击"处理订单"按钮
3. 系统会使用AI处理订单信息
4. 处理完成后显示结果预览

### 编辑和确认

1. 在结果预览区域查看处理结果
2. 点击"编辑结果"可手动修改
3. 点击"重新处理"可重新AI处理
4. 确认无误后点击"创建订单"

### 创建订单

1. 系统会自动调用GoMyHire API
2. 显示每个订单的创建状态
3. 成功创建的订单会显示详细信息
4. 失败的订单会显示错误原因

## 订单处理规则

### 日期处理
- 自动修正过期日期
- 智能推算未来日期
- 处理跨月跨年情况
- 结果默认为2025年

### 时间计算
- **接机**：使用航班到达时间
- **送机**：航班起飞时间减去3.5小时

### 地点处理
- **接机**：pickup = klia, drop = 酒店英文名
- **送机**：pickup = 酒店英文名, drop = klia
- 自动转换中文酒店名为英文

### 参考号生成
- 基于日期+时间+航班号+客人姓名
- 确保唯一性
- **通用格式**：`202402141422-AK5747-余欣怡`
- **Chong Dealer格式**：`202402141422-AK5747-余欣怡-CHONG`
- 自动重复检测，确保系统内唯一性

### 举牌服务处理
- **自动识别**：检测订单中的"举牌"、"接机牌"、"meet and greet"等关键词
- **智能分离**：为接机订单自动生成独立的举牌服务订单
- **客人信息提取**：自动提取客人姓名用于举牌服务
- **关联管理**：举牌服务订单与原订单通过parent_order_id关联

## 文件结构

```
/
├── index.html              # 主页面
├── README.md              # 项目说明
├── core/                  # 核心模块
│   ├── app.js            # 主应用入口（精简版）
│   ├── config.js         # 统一配置
│   ├── logger.js         # 日志系统
│   └── prompts.js        # AI提示词配置
├── services/              # 业务服务
│   ├── api-service.js    # API调用服务
│   ├── llm-service.js    # LLM处理服务
│   ├── order-parser.js   # 订单解析服务
│   └── image-service.js  # 图像处理服务
├── assets/               # 静态资源
│   ├── styles.css       # 主样式文件
│   └── logger.css       # 日志样式文件
├── docs/                 # 统一文档
│   ├── user-guide.md    # 用户指南
│   ├── api-reference.md # API参考
│   ├── development.md   # 开发文档
│   └── api-list.txt     # 原始API列表
└── data/                 # 数据文件
    └── test-orders.txt  # 测试数据
```

## 项目文档

### 📚 文档结构

项目采用统一的文档管理，所有文档集中在docs目录：

1. **[用户指南](docs/user-guide.md)** - 系统使用说明、操作指南、常见问题
2. **[API参考](docs/api-reference.md)** - 完整的API接口文档、请求响应格式
3. **[开发文档](docs/development.md)** - 系统架构、开发规范、部署指南
4. **[API列表](docs/api-list.txt)** - 原始API接口列表和说明

## API接口说明

### GoMyHire API

#### 1. 登录接口
```
POST https://staging.gomyhire.com.my/api/login
Body: {
  "email": "<EMAIL>",
  "password": "1234"
}
```

#### 2. 获取后台用户
```
GET https://staging.gomyhire.com.my/api/backend_users?search=
Headers: {
  "Authorization": "Bearer {token}"
}
```

#### 3. 获取子分类
```
GET https://staging.gomyhire.com.my/api/sub_category?search=
Headers: {
  "Authorization": "Bearer {token}"
}
```

#### 4. 获取车型
```
GET https://staging.gomyhire.com.my/api/car_types?search=
Headers: {
  "Authorization": "Bearer {token}"
}
```

#### 5. 创建订单
```
POST https://staging.gomyhire.com.my/api/create_order
Body: {
  "sub_category_id": 1,
  "car_type_id": 1,
  "incharge_by_backend_user_id": 1,
  "ota_reference_number": "OTA20241219...",
  "customer_name": "张梦媛",
  "customer_contact": "...",
  "flight_info": "KE671",
  "pickup": "klia",
  "destination": "Santa Grand Signature",
  "date": "2025-01-28",
  "time": "22:20",
  "passenger_number": 6
}
```

### Gemini AI API

```
POST https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={API_KEY}
Body: {
  "contents": [{
    "parts": [{
      "text": "处理提示词..."
    }]
  }]
}
```

## 故障排除

### 常见问题

#### 1. 登录失败
- 检查网络连接
- 确认API地址正确
- 验证登录凭据

#### 2. AI处理失败
- 检查Gemini API Key是否正确
- 确认API配额是否充足
- 检查网络连接

#### 3. 订单创建失败
- 检查必填字段是否完整
- 确认用户权限
- 查看API返回的错误信息

#### 4. 图片上传问题
- 检查文件格式（支持JPG, PNG, GIF, WebP）
- 确认文件大小不超过10MB
- 检查浏览器兼容性

### 调试模式

1. 打开浏览器开发者工具（F12）
2. 查看Console标签页的错误信息
3. 检查Network标签页的API请求
4. 查看Application标签页的LocalStorage数据

## 安全注意事项

1. **API密钥安全**：
   - 不要在公共代码库中暴露API密钥
   - 定期轮换API密钥
   - 使用环境变量存储敏感信息

2. **数据安全**：
   - 敏感数据不会永久存储在本地
   - 使用HTTPS进行所有API通信
   - 定期清理浏览器缓存

3. **访问控制**：
   - 确保只有授权用户可以访问系统
   - 实施适当的会话管理
   - 监控异常访问行为

## 扩展功能

### 计划中的功能
- [ ] 多语言支持
- [ ] 批量文件上传
- [ ] 订单模板管理
- [ ] 数据导出功能
- [ ] 统计报表
- [ ] 移动端适配

### 自定义扩展

1. **添加新的OTA类型**：
   - 在 `config.js` 中添加新的OTA配置
   - 创建对应的处理提示词
   - 更新UI选择器

2. **集成其他AI服务**：
   - 修改 `GeminiService` 类
   - 添加新的API配置
   - 实现相应的接口适配

3. **添加OCR功能**：
   - 集成Tesseract.js或Google Vision API
   - 实现 `ImageService.extractTextFromImage` 方法
   - 优化图片预处理

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 联系方式

如有问题或建议，请联系：
- 邮箱：<EMAIL>
- 项目地址：https://github.com/example/ota-order-system

## 更新日志

### v1.2.0 (2024-12-19)
- **新增功能**：Gemini 状态指示器
  - 实时显示 Gemini AI 连接状态
  - 三种状态：检测中（黄色）、已连接（绿色）、连接失败（红色）
  - 点击手动检测连接状态
  - 每5分钟自动检测连接状态
  - 响应式设计，支持移动设备
  - 详细的连接状态日志记录

### v1.1.0 (2024-12-19)
- **新增功能**：GoMyHire API响应日志记录
  - 详细的API请求和响应日志
  - 可折叠的JSON数据显示
  - 响应时间统计
  - 敏感信息自动脱敏
- **新增功能**：举牌服务识别与处理
  - 自动识别举牌服务关键词
  - 智能生成独立举牌服务订单
  - 客人姓名自动提取
  - 订单关联管理
- **优化功能**：OTA参考号生成规则
  - Chong Dealer类型专用格式
  - 重复检测和唯一性保证
  - 智能后缀添加机制
- **系统优化**：日志系统完善
  - API专用日志方法
  - 增强的错误处理
  - 性能监控功能

### v1.0.0 (2024-12-19)
- 初始版本发布
- 基础订单处理功能
- AI集成和API调用
- 用户界面和交互

---

**注意**：本系统仅用于演示和学习目的。在生产环境中使用前，请确保进行充分的测试和安全评估。