{"systemInfo": {"name": "轻量化LLM驱动OTA订单处理系统", "version": "2.0.0", "description": "基于DeepSeek+Gemini的轻量化订单解析系统", "lastUpdated": "2024-12-19"}, "llmConfig": {"deepseek": {"enabled": true, "priority": 1, "apiEndpoint": "https://api.deepseek.com/v1/chat/completions", "model": "deepseek-chat", "timeout": 15000, "maxRetries": 2, "temperature": 0.1, "maxTokens": 4000, "costPerToken": 1e-06}, "gemini": {"enabled": true, "priority": 2, "apiEndpoint": "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent", "timeout": 30000, "maxRetries": 1, "temperature": 0.1, "maxTokens": 4000, "costPerToken": 2e-06}, "fallback": {"enabled": true, "priority": 3, "timeout": 5000, "confidence": 0.6}}, "otaDetection": {"chong_dealer": {"enabled": true, "priority": 1, "keywordPatterns": ["CHONG 车头", "收单&进单", "\\*京鱼\\*", "\\*野马\\*", "\\*小野马\\*", "\\*kenny\\*", "\\*迹象\\*", "\\*鲸鱼\\*", "用车地点[:：]", "用车时间[:：]", "客人姓名[:：]", "接送机类型[:：]", "结算[:：]", "价格[:：].*?Rm", "举牌", "机场转乘", "包车.*?小时", "进单后.*?emoji", "进系统后.*?send"], "minimumMatches": 1, "confidence": 0.9}, "tee": {"enabled": true, "priority": 2, "keywordPatterns": ["OTA[:：]\\s*Tee", "𝔻𝔾 皇族.*?JB", "客人[:：]Tee", "Evelyn.*?012-2970850", "Nicholas.*?017-5992811", "Teh.*?012-4610963", "<PERSON><PERSON>", "Somerset.*?Damansara"], "minimumMatches": 1, "confidence": 0.8}, "shan": {"enabled": true, "priority": 3, "keywordPatterns": ["~ Shan <PERSON>", "Shan.*?添加", "小野马.*?单"], "minimumMatches": 1, "confidence": 0.7}, "gomyhire": {"enabled": true, "priority": 4, "keywordPatterns": ["GoMyHire", "GMH", "Order ID[:：]\\s*\\d+", "Customer Name[:：]", "Car Type[:：]", "Pickup[:：]", "Destination[:：]"], "minimumMatches": 2, "confidence": 0.8}, "jingyu": {"enabled": true, "priority": 5, "keywordPatterns": ["京鱼旅行", "联系人[:：]", "项目[:：].*?接机", "日期[:：].*?\\d+月\\d+日"], "minimumMatches": 2, "confidence": 0.8}}, "platformPresets": {"chong_dealer": {"defaultCarTypeId": 1, "defaultBackendUserId": 2, "defaultSubCategoryId": 1, "timezone": "Asia/Kuala_Lumpur", "currency": "MYR", "language": "zh-CN", "businessRules": {"requireFlightForPickup": false, "autoAssignCarType": true, "defaultServiceTime": "00:00"}}}, "smartSelection": {"enabled": true, "rules": {"carTypeByPassengerCount": {"1": 1, "2": 1, "3": 1, "4": 1, "5": 2, "6": 2, "7": 2, "8": 3, "9": 3, "10": 3}, "subCategoryByServiceType": {"接机": 1, "送机": 2, "包车": 3, "点对点": 4, "机场转乘": 5}, "backendUserByOta": {"chong_dealer": 2, "gomyhire": 1}}}, "goMyHireApi": {"enabled": true, "endpoint": "https://api.gomyhire.com/v1/orders", "timeout": 10000, "retryAttempts": 3, "dateFormat": "DD-MM-YYYY", "requiredFields": ["customer_name", "service_date", "service_type", "car_type_id", "backend_user_id", "sub_category_id"], "fieldMapping": {"customerName": "customer_name", "customerContact": "customer_contact", "customerEmail": "customer_email", "serviceDate": "service_date", "serviceTime": "service_time", "serviceType": "service_type", "pickupAddress": "pickup_address", "dropoffAddress": "dropoff_address", "carTypeId": "car_type_id", "backendUserId": "backend_user_id", "subCategoryId": "sub_category_id", "flightNumber": "flight_number", "passengerCount": "passenger_count", "specialRequirements": "special_requirements", "otaReference": "ota_reference"}, "validation": {"strictMode": false, "allowPartialData": true, "autoFillDefaults": true}}, "performance": {"targets": {"otaDetectionTime": 100, "llmProcessingTime": 15000, "formatConversionTime": 50, "totalProcessingTime": 17000}, "monitoring": {"enabled": true, "logSlowRequests": true, "slowRequestThreshold": 20000, "trackApiCosts": true}, "optimization": {"enableCaching": false, "cacheExpiryMinutes": 30, "maxConcurrentRequests": 5, "requestQueueSize": 100}}, "errorHandling": {"retryPolicy": {"maxRetries": 3, "backoffMultiplier": 2, "initialDelay": 1000}, "fallbackChain": ["deepseek", "gemini", "fallback_parser", "emergency_order"], "errorNotification": {"enabled": true, "webhookUrl": "", "emailAlerts": false}}, "validation": {"orderValidation": {"enabled": true, "strictMode": false, "requiredFields": ["customerName", "serviceType"], "recommendedFields": ["serviceDate", "flightNumber", "customerContact"], "businessRules": [{"rule": "passengerCount <= 10", "message": "乘客人数不能超过10人"}, {"rule": "serviceType in ['接机', '送机']", "message": "服务类型必须是接机或送机"}]}, "dateValidation": {"enabled": true, "allowPastDates": false, "maxFutureDays": 365, "defaultYear": "current"}}, "logging": {"enabled": true, "level": "info", "destinations": ["console", "file"], "fileConfig": {"maxSize": "10MB", "maxFiles": 5, "filename": "logs/ota-processor.log"}, "includeFields": {"originalText": false, "apiResponses": false, "processingTime": true, "errorDetails": true}}, "testing": {"enabled": true, "testDataPath": "_chat.txt", "benchmarkTargets": {"accuracyRate": 0.95, "processingTime": 17000, "successRate": 0.98}, "validationSamples": [{"name": "标准订单", "text": "用车地点：吉隆坡\n用车时间：03月23日 17点，3大人（7座）\n客人姓名：黄子慧3人\n航班号：CZ8301\n接机/送机：接机", "expectedOrders": 1}, {"name": "多程订单", "text": "3月2日：吉隆坡接机\nD7343, 08:50抵达\n\n3月4日：吉隆坡送机\nAK5740，18:15起飞", "expectedOrders": 2}]}, "security": {"apiKeyEncryption": true, "dataRetention": {"originalText": "7days", "processedOrders": "30days", "logs": "90days"}, "sensitiveDataMasking": {"enabled": true, "maskFields": ["customerContact", "customerEmail"], "maskPattern": "***"}}, "integration": {"webhooks": {"enabled": false, "onOrderProcessed": "", "onError": "", "timeout": 5000}, "database": {"enabled": false, "connectionString": "", "tableName": "processed_orders"}}}