{"otaPlatforms": {"chong_dealer": {"name": "Chong Dealer", "description": "Chong Dealer OTA平台订单格式", "enabled": true, "priority": 1, "detectionPatterns": ["CHONG 车头", "用车地点[:：]", "接送机类型[:：]", "\\*京鱼\\*", "\\*野马\\*", "结算[:：].*?\\d+"], "fieldMappings": {"customerName": ["姓名", "客人姓名", "客人"], "passengerCount": ["人数", "乘客人数", "大人"], "serviceType": ["接送机类型", "服务类型"], "serviceDate": ["用车时间", "服务日期"], "flightNumber": ["航班号"], "carType": ["车型"], "hotel": ["酒店", "住宿"], "phone": ["联系方式", "电话"], "wechat": ["微信号", "微信"], "pricing": ["结算", "价格"]}, "separators": ["^-{3,}$", "^\\d+\\.", "^\\d{1,2}[月\\.]\\d{1,2}日?[:：]", "^\\*[^*]+\\*$", "^_{10,}$"], "specialServices": {"signService": "举牌", "transferService": "机场转乘|转站楼", "charterService": "包车.*?(\\d+)小时"}, "carTypeMapping": {"经济": "Compact 5 Seater", "舒适": "Comfort 7 Seater", "豪华": "Premium 10 Seater", "阿尔法": "Premium 10 Seater", "Velfire": "Premium 10 Seater", "Alphard": "Premium 10 Seater"}, "validationRules": {"required": ["customerName", "serviceType"], "recommended": ["serviceDate", "flightNumber", "hotel"], "businessRules": [{"rule": "passengerCount <= seatCount", "message": "乘客人数不能超过车辆座位数"}, {"rule": "serviceType === '接机' && flightNumber", "message": "接机服务建议提供航班号"}, {"rule": "serviceType === '送机' && (flightTime || serviceTime)", "message": "送机服务建议提供航班时间"}]}}, "gomyhire": {"name": "GoMyHire", "description": "GoMyHire平台标准格式", "enabled": false, "priority": 2, "detectionPatterns": ["Order ID:", "OTA Reference Number:", "Customer Name:", "Pickup:", "Destination:", "Car Type:"], "fieldMappings": {"customerName": ["Customer Name"], "orderId": ["Order ID"], "otaReference": ["OTA Reference Number"], "pickupAddress": ["Pickup"], "dropoffAddress": ["Destination"], "carType": ["Car Type"], "serviceDate": ["Date"], "customerContact": ["Customer Contact"], "customerEmail": ["Customer <PERSON><PERSON>"]}}, "klook": {"name": "Klook", "description": "Klook平台订单格式", "enabled": false, "priority": 3, "detectionPatterns": ["Klook", "Booking Reference:", "Activity:", "Traveler:", "Date & Time:"]}}, "globalSettings": {"defaultCarType": "Compact 5 Seater", "defaultSeatCount": 5, "timeZone": "Asia/Kuala_Lumpur", "currency": "MYR", "language": "zh-CN", "dateFormats": ["MM-DD", "<PERSON><PERSON><PERSON>", "M月D日", "YYYY-MM-DD"], "timeFormats": ["HH:mm", "H点", "H:mm", "HH.mm"]}, "smartSelection": {"enabled": true, "rules": {"carTypeByPassengerCount": {"1-4": "Compact 5 Seater", "5-7": "Comfort 7 Seater", "8-10": "Premium 10 Seater"}, "backendUserByOta": {"chong_dealer": "chong_operator", "gomyhire": "gomyhire_operator", "klook": "klook_operator"}, "subCategoryByServiceType": {"接机": "airport_pickup", "送机": "airport_dropoff", "包车": "charter_service", "点对点": "point_to_point"}}}, "logging": {"enabled": true, "level": "info", "includeOriginalText": false, "maxLogSize": "10MB", "retentionDays": 30}, "performance": {"maxTextLength": 1000000, "maxOrdersPerParse": 1000, "timeoutMs": 30000, "enableCaching": true, "cacheExpiryMinutes": 60}, "integration": {"gomyhire": {"enabled": true, "apiEndpoint": "https://api.gomyhire.com/v1/orders", "timeout": 15000, "retryAttempts": 3, "fieldMapping": {"customerName": "customer_name", "customerContact": "customer_contact", "serviceType": "service_type", "pickupAddress": "pickup_address", "dropoffAddress": "dropoff_address", "carType": "car_type", "serviceDate": "service_date", "serviceTime": "service_time", "flightNumber": "flight_number", "specialRequirements": "special_requirements"}}, "deepseek": {"enabled": true, "apiEndpoint": "https://api.deepseek.com/v1/chat/completions", "model": "deepseek-chat", "timeout": 15000, "fallbackToGemini": true, "systemPrompt": "你是一个专业的OTA订单解析助手，请根据提供的文本提取订单信息。"}, "gemini": {"enabled": true, "model": "gemini-pro", "timeout": 30000, "systemPrompt": "Extract order information from the provided text and format it as structured data."}}}