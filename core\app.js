/**
 * @file app.js - OTA订单处理系统主应用
 * @description 精简的主应用入口，整合各个服务模块
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 */

// 应用状态管理
class AppState {
    constructor() {
        this.token = localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.TOKEN);
        this.userInfo = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.USER_INFO) || 'null');
        this.backendUsers = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.BACKEND_USERS) || '[]');
        this.subCategories = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.SUB_CATEGORIES) || '[]');
        this.carTypes = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.CAR_TYPES) || '[]');
        this.processedOrders = [];
    }

    /**
     * @function setToken - 设置认证令牌
     * @param {string} token - 认证令牌
     */
    setToken(token) {
        this.token = token;
        localStorage.setItem(SYSTEM_CONFIG.STORAGE_KEYS.TOKEN, token);
    }

    /**
     * @function setUserInfo - 设置用户信息
     * @param {object} userInfo - 用户信息对象
     */
    setUserInfo(userInfo) {
        this.userInfo = userInfo;
        localStorage.setItem(SYSTEM_CONFIG.STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo));
    }

    /**
     * @function clearAuth - 清除认证信息
     */
    clearAuth() {
        this.token = null;
        this.userInfo = null;
        localStorage.removeItem(SYSTEM_CONFIG.STORAGE_KEYS.TOKEN);
        localStorage.removeItem(SYSTEM_CONFIG.STORAGE_KEYS.USER_INFO);
    }

    /**
     * @function cacheSystemData - 缓存系统数据
     * @param {string} key - 缓存键
     * @param {any} data - 要缓存的数据
     */
    cacheSystemData(key, data) {
        this[key] = data;
        const storageKey = SYSTEM_CONFIG.STORAGE_KEYS[key.toUpperCase()];
        if (storageKey) {
            localStorage.setItem(storageKey, JSON.stringify(data));
        }
    }
}

// 主应用类
class OTAOrderApp {
    constructor() {
        this.appState = new AppState();
        this.apiService = new ApiService(this.appState);
        this.llmService = new LLMService();
        this.orderParser = new OrderParser(this.llmService);
        this.imageService = new ImageService();
        this.isInitialized = false;
    }

    /**
     * @function initialize - 初始化应用
     */
    async initialize() {
        if (this.isInitialized) return;

        logger.info('应用', '开始初始化OTA订单处理系统');

        try {
            // 1. 初始化UI组件
            this.initializeUI();

            // 2. 绑定事件监听器
            this.bindEventListeners();

            // 3. 检查登录状态
            await this.checkAuthStatus();

            // 4. 初始化LLM连接检测
            this.initializeLLMStatus();

            // 5. 加载系统数据
            await this.loadSystemData();

            this.isInitialized = true;
            logger.success('应用', '系统初始化完成');

        } catch (error) {
            logger.error('应用', '系统初始化失败', { error: error.message });
            throw error;
        }
    }

    /**
     * @function initializeUI - 初始化UI组件
     */
    initializeUI() {
        // 显示登录模态框（如果未登录）
        if (!this.appState.token) {
            this.showLoginModal();
        }

        // 初始化状态指示器
        this.updateConnectionStatus();

        // 初始化文件上传区域
        this.initializeFileUpload();

        logger.debug('应用', 'UI组件初始化完成');
    }

    /**
     * @function bindEventListeners - 绑定事件监听器
     */
    bindEventListeners() {
        // 登录表单
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // 处理订单按钮
        const processBtn = document.getElementById('processBtn');
        if (processBtn) {
            processBtn.addEventListener('click', () => this.handleProcessOrder());
        }

        // 创建订单按钮
        const createBtn = document.getElementById('createOrderBtn');
        if (createBtn) {
            createBtn.addEventListener('click', () => this.handleCreateOrders());
        }

        // 文件上传
        const fileInput = document.getElementById('imageUpload');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        }

        // 拖拽上传
        const dropZone = document.getElementById('imageDropZone');
        if (dropZone) {
            dropZone.addEventListener('dragover', (e) => this.handleDragOver(e));
            dropZone.addEventListener('drop', (e) => this.handleFileDrop(e));
        }

        // LLM状态指示器点击事件
        const deepseekIndicator = document.getElementById('deepseekStatusIndicator');
        if (deepseekIndicator) {
            deepseekIndicator.addEventListener('click', () => this.handleLLMStatusClick('deepseek'));
        }

        const geminiIndicator = document.getElementById('geminiStatusIndicator');
        if (geminiIndicator) {
            geminiIndicator.addEventListener('click', () => this.handleLLMStatusClick('gemini'));
        }

        logger.debug('应用', '事件监听器绑定完成');
    }

    /**
     * @function checkAuthStatus - 检查认证状态
     */
    async checkAuthStatus() {
        if (this.appState.token) {
            logger.info('应用', '发现已保存的认证令牌，验证有效性');
            try {
                // 尝试获取用户数据来验证token有效性
                await this.apiService.getBackendUsers();
                logger.success('应用', '认证令牌有效');
                this.hideLoginModal();
            } catch (error) {
                logger.warn('应用', '认证令牌已失效，需要重新登录');
                this.appState.clearAuth();
                this.showLoginModal();
            }
        }
    }

    /**
     * @function initializeLLMStatus - 初始化LLM状态检测
     */
    initializeLLMStatus() {
        // 立即检测一次
        this.checkLLMConnections();

        // 定期检测（每5分钟）
        setInterval(() => {
            this.checkLLMConnections();
        }, 5 * 60 * 1000);
    }

    /**
     * @function checkLLMConnections - 检测LLM连接状态
     */
    async checkLLMConnections() {
        logger.info('应用', '检测LLM服务连接状态');

        try {
            // 并行检测DeepSeek和Gemini
            const [deepseekStatus, geminiStatus] = await Promise.all([
                this.llmService.checkDeepSeekConnection(),
                this.llmService.checkGeminiConnection()
            ]);

            // 更新UI状态指示器
            this.updateLLMStatusUI(deepseekStatus, geminiStatus);

        } catch (error) {
            logger.error('应用', 'LLM连接检测失败', { error: error.message });
        }
    }

    /**
     * @function loadSystemData - 加载系统数据
     */
    async loadSystemData() {
        if (!this.appState.token) return;

        logger.info('应用', '加载系统数据');

        try {
            // 并行加载所有系统数据
            await Promise.all([
                this.apiService.getBackendUsers(),
                this.apiService.getSubCategories(),
                this.apiService.getCarTypes()
            ]);

            // 更新UI选择器
            this.updateUISelectors();

            logger.success('应用', '系统数据加载完成');

        } catch (error) {
            logger.error('应用', '系统数据加载失败', { error: error.message });
        }
    }

    /**
     * @function handleLogin - 处理登录
     * @param {Event} event - 表单提交事件
     */
    async handleLogin(event) {
        event.preventDefault();
        
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        logger.info('应用', '开始用户登录', { email });

        try {
            const result = await this.apiService.login(email, password);
            
            if (result.success) {
                logger.success('应用', '登录成功');
                this.hideLoginModal();
                await this.loadSystemData();
            }

        } catch (error) {
            logger.error('应用', '登录失败', { error: error.message });
            this.showError('登录失败: ' + error.message);
        }
    }

    /**
     * @function handleProcessOrder - 处理订单
     */
    async handleProcessOrder() {
        const textInput = document.getElementById('orderText').value.trim();
        const otaType = document.getElementById('otaSelect').value;

        if (!textInput) {
            this.showError('请输入订单文本');
            return;
        }

        logger.info('应用', '开始处理订单', {
            textLength: textInput.length,
            otaType: otaType
        });

        try {
            this.showLoading('正在处理订单...');

            const result = await this.orderParser.parseOrders(textInput, otaType);

            if (result.success) {
                this.appState.processedOrders = result.orders;
                this.displayOrderResults(result);
                logger.success('应用', '订单处理完成', {
                    orderCount: result.orders.length
                });
            } else {
                throw new Error(result.error || '订单处理失败');
            }

        } catch (error) {
            logger.error('应用', '订单处理失败', { error: error.message });
            this.showError('订单处理失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * @function handleCreateOrders - 创建订单
     */
    async handleCreateOrders() {
        if (!this.appState.processedOrders || this.appState.processedOrders.length === 0) {
            this.showError('没有可创建的订单');
            return;
        }

        logger.info('应用', '开始创建订单', {
            orderCount: this.appState.processedOrders.length
        });

        try {
            this.showLoading('正在创建订单...');

            const results = [];
            for (const order of this.appState.processedOrders) {
                try {
                    const result = await this.apiService.createOrder(order);
                    results.push({ success: true, order, result });
                } catch (error) {
                    results.push({ success: false, order, error: error.message });
                }
            }

            this.displayCreateResults(results);
            logger.success('应用', '订单创建完成', {
                totalOrders: results.length,
                successCount: results.filter(r => r.success).length
            });

        } catch (error) {
            logger.error('应用', '订单创建失败', { error: error.message });
            this.showError('订单创建失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * @function handleFileUpload - 处理文件上传
     * @param {Event} event - 文件选择事件
     */
    async handleFileUpload(event) {
        const files = event.target.files;
        if (!files || files.length === 0) return;

        await this.processUploadedFiles(files);
    }

    /**
     * @function handleFileDrop - 处理文件拖拽
     * @param {Event} event - 拖拽事件
     */
    async handleFileDrop(event) {
        event.preventDefault();
        const files = event.dataTransfer.files;
        if (!files || files.length === 0) return;

        await this.processUploadedFiles(files);
    }

    /**
     * @function handleLLMStatusClick - 处理LLM状态指示器点击
     * @param {string} llmType - LLM类型 ('deepseek' 或 'gemini')
     */
    async handleLLMStatusClick(llmType) {
        logger.info('应用', `手动检测${llmType}连接状态`);

        try {
            // 显示检测中状态
            this.setLLMStatusChecking(llmType);

            let isConnected = false;
            if (llmType === 'deepseek') {
                isConnected = await this.llmService.checkDeepSeekConnection();
            } else if (llmType === 'gemini') {
                isConnected = await this.llmService.checkGeminiConnection();
            }

            // 更新单个LLM的状态
            this.updateSingleLLMStatus(llmType, isConnected);

            logger.success('应用', `${llmType}连接检测完成`, { isConnected });

        } catch (error) {
            logger.error('应用', `${llmType}连接检测失败`, { error: error.message });
            this.updateSingleLLMStatus(llmType, false);
        }
    }

    /**
     * @function setLLMStatusChecking - 设置LLM状态为检测中
     * @param {string} llmType - LLM类型
     */
    setLLMStatusChecking(llmType) {
        const lightId = llmType === 'deepseek' ? 'deepseekStatusLight' : 'geminiStatusLight';
        const textId = llmType === 'deepseek' ? 'deepseekStatusText' : 'geminiStatusText';

        const light = document.getElementById(lightId);
        const text = document.getElementById(textId);

        if (light) {
            light.className = 'status-light checking';
        }

        if (text) {
            text.textContent = `${llmType === 'deepseek' ? 'DeepSeek' : 'Gemini'} 检测中...`;
        }
    }

    /**
     * @function updateSingleLLMStatus - 更新单个LLM状态
     * @param {string} llmType - LLM类型
     * @param {boolean} isConnected - 连接状态
     */
    updateSingleLLMStatus(llmType, isConnected) {
        const lightId = llmType === 'deepseek' ? 'deepseekStatusLight' : 'geminiStatusLight';
        const textId = llmType === 'deepseek' ? 'deepseekStatusText' : 'geminiStatusText';
        const indicatorId = llmType === 'deepseek' ? 'deepseekStatusIndicator' : 'geminiStatusIndicator';

        const light = document.getElementById(lightId);
        const text = document.getElementById(textId);
        const indicator = document.getElementById(indicatorId);

        const displayName = llmType === 'deepseek' ? 'DeepSeek' : 'Gemini';

        if (light) {
            light.className = isConnected ? 'status-light connected' : 'status-light disconnected';
        }

        if (text) {
            text.textContent = isConnected ? `${displayName} 已连接` : `${displayName} 连接失败`;
        }

        if (indicator) {
            indicator.title = isConnected ? `${displayName} API 连接正常` : `${displayName} API 连接失败`;
            const baseClass = `llm-status-indicator ${llmType}-indicator`;
            indicator.className = isConnected ? `${baseClass} connected` : `${baseClass} disconnected`;
        }
    }

    /**
     * @function processUploadedFiles - 处理上传的文件
     * @param {FileList} files - 文件列表
     */
    async processUploadedFiles(files) {
        logger.info('应用', '开始处理上传的文件', { fileCount: files.length });

        try {
            this.showLoading('正在处理图片...');

            // 处理图片文件
            const imageResults = await this.imageService.processImageFiles(files);

            if (imageResults.success) {
                // 从图片中提取订单信息
                const orderResults = await this.imageService.extractOrdersFromImages(imageResults.results);

                if (orderResults.success) {
                    // 将提取的文本填入文本框
                    document.getElementById('orderText').value = orderResults.combinedText;
                    
                    // 自动处理订单
                    this.appState.processedOrders = orderResults.orders;
                    this.displayOrderResults(orderResults);
                }
            }

            this.displayImageResults(imageResults);

        } catch (error) {
            logger.error('应用', '文件处理失败', { error: error.message });
            this.showError('文件处理失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    // UI辅助方法
    /**
     * @function showLoginModal - 显示登录模态框
     */
    showLoginModal() {
        const modal = document.getElementById('loginModal');
        const mainApp = document.getElementById('mainApp');

        if (modal) {
            modal.style.display = 'block';
            logger.debug('UI', '显示登录模态框');
        }

        // 隐藏主界面
        if (mainApp) {
            mainApp.classList.add('hidden');
            logger.debug('UI', '隐藏主界面');
        }
    }

    /**
     * @function hideLoginModal - 隐藏登录模态框
     */
    hideLoginModal() {
        const modal = document.getElementById('loginModal');
        const mainApp = document.getElementById('mainApp');

        if (modal) {
            modal.style.display = 'none';
            logger.debug('UI', '隐藏登录模态框');
        }

        // 显示主界面
        if (mainApp) {
            mainApp.classList.remove('hidden');
            logger.debug('UI', '显示主界面');
        }
    }

    /**
     * @function showLoading - 显示加载状态
     * @param {string} message - 加载消息
     */
    showLoading(message) {
        const loadingModal = document.getElementById('loadingModal');
        const messageDiv = document.getElementById('loadingText');

        if (loadingModal) {
            loadingModal.classList.remove('hidden');
            loadingModal.style.display = 'block';
        }

        if (messageDiv && message) {
            messageDiv.textContent = message;
        }

        logger.debug('UI', '显示加载状态', { message });
    }

    /**
     * @function hideLoading - 隐藏加载状态
     */
    hideLoading() {
        const loadingModal = document.getElementById('loadingModal');
        if (loadingModal) {
            loadingModal.classList.add('hidden');
            loadingModal.style.display = 'none';
        }
        logger.debug('UI', '隐藏加载状态');
    }

    /**
     * @function showError - 显示错误消息
     * @param {string} message - 错误消息
     */
    showError(message) {
        const errorDiv = document.getElementById('loginError');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // 也在控制台显示错误
        logger.error('UI', '显示错误消息', { message });

        // 3秒后自动隐藏错误消息
        setTimeout(() => {
            if (errorDiv) {
                errorDiv.style.display = 'none';
            }
        }, 3000);
    }

    /**
     * @function updateConnectionStatus - 更新连接状态
     */
    updateConnectionStatus() {
        // 这里可以添加连接状态更新逻辑
        logger.debug('UI', '更新连接状态');
    }

    /**
     * @function updateLLMStatusUI - 更新LLM状态UI
     * @param {boolean} deepseek - DeepSeek连接状态
     * @param {boolean} gemini - Gemini连接状态
     */
    updateLLMStatusUI(deepseek, gemini) {
        // 更新DeepSeek状态指示器
        const deepseekLight = document.getElementById('deepseekStatusLight');
        const deepseekText = document.getElementById('deepseekStatusText');
        const deepseekIndicator = document.getElementById('deepseekStatusIndicator');

        if (deepseekLight) {
            deepseekLight.className = deepseek ? 'status-light connected' : 'status-light disconnected';
        }

        if (deepseekText) {
            deepseekText.textContent = deepseek ? 'DeepSeek 已连接' : 'DeepSeek 连接失败';
        }

        if (deepseekIndicator) {
            deepseekIndicator.title = deepseek ? 'DeepSeek API 连接正常' : 'DeepSeek API 连接失败';
            deepseekIndicator.className = deepseek ?
                'llm-status-indicator deepseek-indicator connected' :
                'llm-status-indicator deepseek-indicator disconnected';
        }

        // 更新Gemini状态指示器
        const geminiLight = document.getElementById('geminiStatusLight');
        const geminiText = document.getElementById('geminiStatusText');
        const geminiIndicator = document.getElementById('geminiStatusIndicator');

        if (geminiLight) {
            geminiLight.className = gemini ? 'status-light connected' : 'status-light disconnected';
        }

        if (geminiText) {
            geminiText.textContent = gemini ? 'Gemini 已连接' : 'Gemini 连接失败';
        }

        if (geminiIndicator) {
            geminiIndicator.title = gemini ? 'Gemini API 连接正常' : 'Gemini API 连接失败';
            geminiIndicator.className = gemini ?
                'llm-status-indicator gemini-indicator connected' :
                'llm-status-indicator gemini-indicator disconnected';
        }

        logger.debug('UI', '更新LLM状态UI', {
            deepseek,
            gemini,
            deepseekElements: {
                light: !!deepseekLight,
                text: !!deepseekText,
                indicator: !!deepseekIndicator
            },
            geminiElements: {
                light: !!geminiLight,
                text: !!geminiText,
                indicator: !!geminiIndicator
            }
        });
    }

    /**
     * @function updateUISelectors - 更新UI选择器
     */
    updateUISelectors() {
        // 更新后台用户选择器
        this.updateBackendUserSelector();

        // 更新子分类选择器
        this.updateSubCategorySelector();

        // 更新车型选择器
        this.updateCarTypeSelector();

        logger.debug('UI', 'UI选择器更新完成');
    }

    /**
     * @function updateBackendUserSelector - 更新后台用户选择器
     */
    updateBackendUserSelector() {
        const selector = document.getElementById('backendUserSelect');
        if (selector && this.appState.backendUsers) {
            selector.innerHTML = '<option value="">选择用户</option>';

            this.appState.backendUsers.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.name} (${user.role_id})`;
                selector.appendChild(option);
            });
        }
    }

    /**
     * @function updateSubCategorySelector - 更新子分类选择器
     */
    updateSubCategorySelector() {
        const selector = document.getElementById('subCategorySelect');
        if (selector && this.appState.subCategories) {
            selector.innerHTML = '<option value="">选择分类</option>';

            this.appState.subCategories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = `${category.main_category} - ${category.name}`;
                selector.appendChild(option);
            });
        }
    }

    /**
     * @function updateCarTypeSelector - 更新车型选择器
     */
    updateCarTypeSelector() {
        const selector = document.getElementById('carTypeSelect');
        if (selector && this.appState.carTypes) {
            selector.innerHTML = '<option value="">选择车型</option>';

            this.appState.carTypes.forEach(carType => {
                const option = document.createElement('option');
                option.value = carType.id;
                option.textContent = `${carType.type} (${carType.seat_number}座)`;
                selector.appendChild(option);
            });
        }
    }

    /**
     * @function displayOrderResults - 显示订单结果
     * @param {object} results - 订单处理结果
     */
    displayOrderResults(results) {
        const resultsDiv = document.getElementById('orderResults');
        const resultSection = document.getElementById('resultPreview');

        if (!resultsDiv) return;

        if (results.success && results.orders && results.orders.length > 0) {
            let html = '<h3>处理结果</h3>';
            html += `<p>共处理 ${results.orders.length} 个订单</p>`;

            results.orders.forEach((order, index) => {
                html += `<div class="order-result">`;
                html += `<h4>订单 ${index + 1}</h4>`;
                html += `<p><strong>客人姓名:</strong> ${order.customerName || '未知'}</p>`;
                html += `<p><strong>服务类型:</strong> ${order.serviceType || '未知'}</p>`;
                html += `<p><strong>服务日期:</strong> ${order.serviceDate || '未知'}</p>`;
                html += `<p><strong>航班信息:</strong> ${order.flightNumber || '未知'}</p>`;
                html += `</div>`;
            });

            resultsDiv.innerHTML = html;

            // 显示结果预览区域
            if (resultSection) {
                resultSection.classList.remove('hidden');
            }
        } else {
            resultsDiv.innerHTML = '<p>未能处理订单信息</p>';
        }

        logger.debug('UI', '显示订单结果', { orderCount: results.orders?.length || 0 });
    }

    /**
     * @function displayCreateResults - 显示创建结果
     * @param {array} results - 创建结果数组
     */
    displayCreateResults(results) {
        const resultsDiv = document.getElementById('createResults');
        const statusSection = document.getElementById('orderStatus');

        if (!resultsDiv) return;

        let html = '<h3>创建结果</h3>';
        const successCount = results.filter(r => r.success).length;
        html += `<p>成功创建 ${successCount}/${results.length} 个订单</p>`;

        results.forEach((result, index) => {
            html += `<div class="create-result ${result.success ? 'success' : 'error'}">`;
            html += `<h4>订单 ${index + 1}</h4>`;
            if (result.success) {
                html += `<p>✅ 创建成功</p>`;
                html += `<p>订单ID: ${result.result?.id || '未知'}</p>`;
            } else {
                html += `<p>❌ 创建失败: ${result.error}</p>`;
            }
            html += `</div>`;
        });

        resultsDiv.innerHTML = html;

        // 显示订单状态区域
        if (statusSection) {
            statusSection.classList.remove('hidden');
        }

        logger.debug('UI', '显示创建结果', { successCount, totalCount: results.length });
    }

    /**
     * @function displayImageResults - 显示图片结果
     * @param {object} results - 图片处理结果
     */
    displayImageResults(results) {
        const resultsDiv = document.getElementById('imageResults');
        if (!resultsDiv) return;

        let html = '<h3>图片处理结果</h3>';
        html += `<p>成功处理 ${results.processedFiles}/${results.totalFiles} 个文件</p>`;

        if (results.results && results.results.length > 0) {
            results.results.forEach((result, index) => {
                html += `<div class="image-result">`;
                html += `<h4>${result.fileName}</h4>`;
                if (result.success) {
                    html += `<p>✅ 处理成功</p>`;
                    html += `<p>识别文字长度: ${result.ocrResult?.text?.length || 0} 字符</p>`;
                } else {
                    html += `<p>❌ 处理失败: ${result.error}</p>`;
                }
                html += `</div>`;
            });
        }

        resultsDiv.innerHTML = html;
        logger.debug('UI', '显示图片结果', { processedFiles: results.processedFiles });
    }

    /**
     * @function initializeFileUpload - 初始化文件上传
     */
    initializeFileUpload() {
        const dropZone = document.getElementById('imageDropZone');
        if (dropZone) {
            dropZone.addEventListener('dragover', (e) => this.handleDragOver(e));
            dropZone.addEventListener('drop', (e) => this.handleFileDrop(e));
        }
        logger.debug('UI', '文件上传初始化完成');
    }

    /**
     * @function handleDragOver - 处理拖拽悬停
     * @param {Event} event - 拖拽事件
     */
    handleDragOver(event) {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'copy';
    }
}

// 全局应用实例
let app;

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', async () => {
    try {
        app = new OTAOrderApp();
        await app.initialize();
    } catch (error) {
        console.error('应用初始化失败:', error);
    }
});

// 导出应用类
if (typeof window !== 'undefined') {
    window.OTAOrderApp = OTAOrderApp;
}
