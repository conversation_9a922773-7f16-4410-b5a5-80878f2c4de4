<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试版本 - OTA订单处理系统</title>
    <link rel="stylesheet" href="assets/styles.css">
    <link rel="stylesheet" href="assets/logger.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        #debugConsole {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 200px;
            background: #f8f9fa;
            border-top: 2px solid #007bff;
            padding: 10px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            z-index: 9999;
        }
        #debugConsole .debug-log {
            margin: 2px 0;
            padding: 2px 5px;
        }
        #debugConsole .debug-error {
            background: #f8d7da;
            color: #721c24;
        }
        #debugConsole .debug-success {
            background: #d4edda;
            color: #155724;
        }
        #debugConsole .debug-info {
            background: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div id="debugConsole">
        <div class="debug-log debug-info">🔧 调试控制台已启动</div>
    </div>

    <!-- 登录窗口 -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <h2>系统登录</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">邮箱:</label>
                    <input type="email" id="email" value="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" value="1234" required>
                </div>
                <button type="submit">登录</button>
                <div id="loginError" class="error-message"></div>
            </form>
        </div>
    </div>

    <!-- 主界面 -->
    <div id="mainApp" class="hidden">
        <header>
            <h1>OTA订单处理系统</h1>
            <div class="header-controls">
                <div class="user-info">
                    <span id="userInfo">已登录</span>
                    <button id="logoutBtn">退出登录</button>
                </div>
            </div>
        </header>

        <main>
            <section id="orderInput" class="section">
                <h2>订单内容输入</h2>
                <div class="input-tabs">
                    <button class="tab-btn active" data-tab="text">文字输入</button>
                </div>

                <div id="textInput" class="tab-content active">
                    <textarea id="orderText" placeholder="请输入订单内容..."></textarea>
                </div>

                <div class="ota-selection">
                    <label for="otaSelect">选择OTA类型:</label>
                    <select id="otaSelect">
                        <option value="auto">自动识别</option>
                        <option value="chong-dealer">Chong Dealer</option>
                        <option value="other">其他</option>
                    </select>
                </div>

                <button id="processBtn" class="primary-btn">处理订单</button>
            </section>

            <section id="resultPreview" class="section hidden">
                <h2>处理结果预览</h2>
                <div id="resultContent" class="result-content">
                    <div id="orderResults"></div>
                </div>
                <div class="action-buttons">
                    <button id="createOrderBtn" class="primary-btn">创建订单</button>
                </div>
            </section>
        </main>
    </div>

    <!-- 加载提示 -->
    <div id="loadingModal" class="modal hidden">
        <div class="modal-content">
            <div class="loading-spinner"></div>
            <p id="loadingText">正在处理...</p>
        </div>
    </div>

    <script>
        // 调试日志函数
        function debugLog(message, type = 'info', data = null) {
            const console = document.getElementById('debugConsole');
            const time = new Date().toLocaleTimeString();
            let logMessage = `[${time}] ${message}`;
            if (data) {
                logMessage += ': ' + JSON.stringify(data, null, 2);
            }
            
            const logDiv = document.createElement('div');
            logDiv.className = `debug-log debug-${type}`;
            logDiv.textContent = logMessage;
            console.appendChild(logDiv);
            console.scrollTop = console.scrollHeight;
            
            // 也输出到浏览器控制台
            console.log(`[DEBUG] ${message}`, data);
        }

        // 错误捕获
        window.addEventListener('error', function(e) {
            debugLog(`JavaScript错误: ${e.message} (${e.filename}:${e.lineno})`, 'error');
        });

        window.addEventListener('unhandledrejection', function(e) {
            debugLog(`未处理的Promise拒绝: ${e.reason}`, 'error');
        });

        debugLog('开始加载脚本文件');
    </script>

    <!-- 引入核心模块 -->
    <script src="core/config.js"></script>
    <script>
        debugLog('config.js 加载完成', 'success', { 
            hasConfig: typeof SYSTEM_CONFIG !== 'undefined',
            configKeys: typeof SYSTEM_CONFIG !== 'undefined' ? Object.keys(SYSTEM_CONFIG) : []
        });
    </script>

    <script src="core/prompts.js"></script>
    <script>
        debugLog('prompts.js 加载完成', 'success', { 
            hasPromptManager: typeof PromptManager !== 'undefined'
        });
    </script>

    <script src="core/logger.js"></script>
    <script>
        debugLog('logger.js 加载完成', 'success', { 
            hasLogger: typeof logger !== 'undefined'
        });
    </script>

    <!-- 引入服务模块 -->
    <script src="services/api-service.js"></script>
    <script>
        debugLog('api-service.js 加载完成', 'success', { 
            hasApiService: typeof ApiService !== 'undefined'
        });
    </script>

    <script src="services/llm-service.js"></script>
    <script>
        debugLog('llm-service.js 加载完成', 'success', { 
            hasLLMService: typeof LLMService !== 'undefined'
        });
    </script>

    <script src="services/order-parser.js"></script>
    <script>
        debugLog('order-parser.js 加载完成', 'success', { 
            hasOrderParser: typeof OrderParser !== 'undefined'
        });
    </script>

    <script src="services/image-service.js"></script>
    <script>
        debugLog('image-service.js 加载完成', 'success', { 
            hasImageService: typeof ImageService !== 'undefined'
        });
    </script>

    <!-- 引入主应用 -->
    <script src="core/app.js"></script>
    <script>
        debugLog('app.js 加载完成', 'success', { 
            hasOTAOrderApp: typeof OTAOrderApp !== 'undefined',
            hasApp: typeof app !== 'undefined'
        });

        // 等待DOM加载完成后检查应用状态
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM加载完成，检查应用状态');
            
            setTimeout(() => {
                debugLog('应用状态检查', 'info', {
                    hasApp: typeof app !== 'undefined',
                    appInitialized: app ? app.isInitialized : false,
                    loginModalVisible: document.getElementById('loginModal').style.display,
                    mainAppVisible: !document.getElementById('mainApp').classList.contains('hidden')
                });
            }, 1000);
        });
    </script>
</body>
</html>
