﻿API List to create order


Obtain data to be passed into create order API, need to login to avoid impostor accessing system data
Data included backend users, car types, sub categories
1. Login
URL: https://staging.gomyhire.com.my/api/login
Body: {
   ‘email’: ‘<EMAIL>’,
   ‘password’: ‘1234’
}
Response: {
   "status": true,
   "token": "2409|F0hye4nKqL58qhcdW8knwLwLMbQltjl35uggBwGA",
}
See token, copy the text after number| , that is the token used for login


2. Obtain system backend users, the user.id need to be passed into create order API and the user will become the operator who incharge of the order (deduct credit, accept email…)
Bearer token: token obtain from login API, such as “F0hye4nKqL58qhcdW8knwLwLMbQltjl35uggBwGA”
        URL: https://staging.gomyhire.com.my/api/backend_users?search=
        Parameter: {
           ‘search’: ‘’
        }
        Response: {
           ‘data’: [
   {
                      ‘id’: 1,
                      ‘name’: ‘super admin name’,
                      ‘phone’: ‘012345689’,
                      ‘role_id’: ‘super admin’
                   },
                   {
                      ‘id’: 2,
                      ‘name’: ‘operator 2’,
                      ‘phone’: ‘012345688’,
                      ‘role_id’: ‘operator’
                   }
                  ]
        }
        ?search= can be used to filter user.id, user.name, user.phone
        user.id is required to be passed into create order API


   3. Obtain system sub categories, the sub_category.id need to be passed into create order API. Sub category got preset order_type, ota, driving region, languages, when call create order API, no need to mention order_type, ota, region, languages, only pass in sub_category.id then system will auto fill based on what has been preset by sub category
Bearer token: token obtain from login API, such as “F0hye4nKqL58qhcdW8knwLwLMbQltjl35uggBwGA”
URL: https://staging.gomyhire.com.my/api/sub_category?search=
API will return where sub_category.id or sub_category.name or category,name matching “search”

   4. Obtain system car types, the car_type.id need to be passed into create order API
Bearer token: token obtain from login API, such as “F0hye4nKqL58qhcdW8knwLwLMbQltjl35uggBwGA”
URL: https://staging.gomyhire.com.my/api/car_types?search=


After obtaining data from backend, someone will create third party page to insert order. Create order API does not require login.
      5. Create order API
URL: https://staging.gomyhire.com.my/api/create_order
No need bearer token
Response: 
sub_category_id, car_type_id, incharge_by_backend_user_id are obtained from the APIs above, these three and ota_reference_number are required to be filled in, others will be optional
{
  "status": false,
  "message": "Data need to be refined",
  "data": {
    "validation_error": {
      "sub_category_id": [
        "The sub category id field is required."
      ],
      "ota_reference_number": [
        "The ota reference number field is required."
      ],
      "car_type_id": [
        "The car type id field is required."
      ],
      "incharge_by_backend_user_id": [
        "The incharge by backend user id field is required."
      ]
    },
    "available_fields_to_fill_in": [
      "sub_category_id",
      "ota",
      "ota_reference_number",
      "ota_price",
      "customer_name",
      "customer_contact",
      "customer_email",
      "flight_info",
      "pickup",
      "pickup_lat",
      "pickup_long",
      "date",
      "time",
      "destination",
      "destination_lat",
      "destination_long",
      "car_type_id",
      "passenger_number",
      "luggage_number",
      "driver_fee",
      "driver_collect",
      "tour_guide",
      "baby_chair",
      "meet_and_greet",
      "extra_requirement",
      "incharge_by_backend_user_id"
    ]
  }
}