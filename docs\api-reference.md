# API参考文档

## 概述

本文档描述了OTA订单处理系统使用的所有API接口，包括GoMyHire API、AI服务API等。

## GoMyHire API

### 基础信息
- **基础URL**: `https://staging.gomyhire.com.my/api`
- **认证方式**: Bearer Token
- **数据格式**: JSON

### 1. 用户登录

获取系统访问令牌，用于后续API调用的身份验证。

**请求**
```http
POST /login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "1234"
}
```

**响应**
```json
{
  "status": true,
  "token": "2409|F0hye4nKqL58qhcdW8knwLwLMbQltjl35uggBwGA",
  "user": {
    "id": 1,
    "name": "Super Admin",
    "email": "<EMAIL>"
  }
}
```

**说明**
- Token格式为 `数字|实际token`，使用时需要提取 `|` 后面的部分
- Token用于后续API调用的Authorization头

### 2. 获取后台用户列表

获取系统中的后台用户信息，用于订单分配。

**请求**
```http
GET /backend_users?search=
Authorization: Bearer {token}
```

**参数**
- `search` (可选): 搜索关键词，可匹配用户ID、姓名、电话

**响应**
```json
{
  "data": [
    {
      "id": 1,
      "name": "super admin name",
      "phone": "012345689",
      "role_id": "super admin"
    },
    {
      "id": 2,
      "name": "operator 2", 
      "phone": "012345688",
      "role_id": "operator"
    }
  ]
}
```

### 3. 获取子分类列表

获取订单子分类信息，包含预设的订单类型、OTA、驾驶区域、语言等。

**请求**
```http
GET /sub_category?search=
Authorization: Bearer {token}
```

**参数**
- `search` (可选): 搜索关键词，可匹配分类ID、分类名称

**响应**
```json
{
  "data": [
    {
      "id": 1,
      "main_category": "Airport Transfer",
      "name": "Airport Pickup",
      "preset_data": {
        "order_type": "pickup",
        "ota": "gomyhire",
        "region": "klang_valley",
        "language": "english"
      },
      "required_fields": ["customer_name", "flight_info", "pickup", "destination"]
    }
  ]
}
```

### 4. 获取车型列表

获取可用的车型信息，包含座位数、优先级等。

**请求**
```http
GET /car_types?search=
Authorization: Bearer {token}
```

**响应**
```json
{
  "data": [
    {
      "id": 1,
      "type": "Comfort 5 Seater",
      "seat_number": 5,
      "priority": 1
    },
    {
      "id": 2,
      "type": "Premium 7 Seater",
      "seat_number": 7,
      "priority": 2
    }
  ]
}
```

### 5. 创建订单

创建新的订单记录。

**请求**
```http
POST /create_order
Content-Type: application/json

{
  "sub_category_id": 1,
  "car_type_id": 1,
  "incharge_by_backend_user_id": 1,
  "ota_reference_number": "OTA20241219001",
  "customer_name": "张三",
  "customer_contact": "13800138000",
  "customer_email": "<EMAIL>",
  "flight_info": "CZ8301",
  "pickup": "klia",
  "destination": "Santa Grand Signature",
  "date": "28-01-2025",
  "time": "22:20",
  "passenger_number": 2,
  "luggage_number": 2,
  "meet_and_greet": true,
  "extra_requirement": "举牌服务"
}
```

**必填字段**
- `sub_category_id`: 子分类ID
- `car_type_id`: 车型ID  
- `incharge_by_backend_user_id`: 负责用户ID
- `ota_reference_number`: OTA参考号

**可选字段**
- `ota`: OTA平台名称
- `ota_price`: OTA价格
- `customer_name`: 客人姓名
- `customer_contact`: 客人联系方式
- `customer_email`: 客人邮箱
- `flight_info`: 航班信息
- `pickup`: 上车地点
- `pickup_lat`: 上车地点纬度
- `pickup_long`: 上车地点经度
- `date`: 服务日期 (DD-MM-YYYY格式)
- `time`: 服务时间 (HH:MM格式)
- `destination`: 目的地
- `destination_lat`: 目的地纬度
- `destination_long`: 目的地经度
- `passenger_number`: 乘客人数
- `luggage_number`: 行李数量
- `driver_fee`: 司机费用
- `driver_collect`: 司机代收
- `tour_guide`: 导游服务
- `baby_chair`: 婴儿座椅
- `meet_and_greet`: 举牌服务
- `extra_requirement`: 额外要求

**响应**
```json
{
  "status": true,
  "message": "Order created successfully",
  "data": {
    "id": 12345,
    "order_number": "GMH20241219001",
    "status": "pending",
    "created_at": "2024-12-19T10:30:00Z"
  }
}
```

## AI服务API

### DeepSeek API

**基础URL**: `https://api.deepseek.com/v1/chat/completions`

**请求示例**
```http
POST /v1/chat/completions
Authorization: Bearer {api_key}
Content-Type: application/json

{
  "model": "deepseek-chat",
  "messages": [
    {
      "role": "user",
      "content": "处理订单文本..."
    }
  ],
  "temperature": 0.4,
  "max_tokens": 2048
}
```

### Gemini API

**基础URL**: `https://generativelanguage.googleapis.com/v1/models/gemini-2.0-flash:generateContent`

**请求示例**
```http
POST /v1/models/gemini-2.0-flash:generateContent?key={api_key}
Content-Type: application/json

{
  "contents": [
    {
      "parts": [
        {
          "text": "处理订单文本..."
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.4,
    "maxOutputTokens": 2048
  }
}
```

### Google Vision API

**基础URL**: `https://vision.googleapis.com/v1/images:annotate`

**请求示例**
```http
POST /v1/images:annotate?key={api_key}
Content-Type: application/json

{
  "requests": [
    {
      "image": {
        "content": "base64_encoded_image"
      },
      "features": [
        {
          "type": "DOCUMENT_TEXT_DETECTION",
          "maxResults": 10
        }
      ]
    }
  ]
}
```

## 错误处理

### 错误响应格式
```json
{
  "status": false,
  "message": "错误描述",
  "data": {
    "validation_error": {
      "field_name": ["错误信息"]
    }
  }
}
```

### 常见错误码
- `400`: 请求参数错误
- `401`: 认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 数据验证失败
- `500`: 服务器内部错误

## 速率限制

- **GoMyHire API**: 每分钟100次请求
- **DeepSeek API**: 每分钟60次请求
- **Gemini API**: 每分钟60次请求
- **Google Vision API**: 每分钟1000次请求

## 最佳实践

### 1. 错误重试
- 实现指数退避重试机制
- 最大重试次数：3次
- 初始延迟：1秒

### 2. 超时设置
- GoMyHire API：10秒
- DeepSeek API：15秒
- Gemini API：30秒
- Google Vision API：15秒

### 3. 数据验证
- 客户端验证必填字段
- 服务端验证数据格式
- 敏感信息脱敏处理

### 4. 日志记录
- 记录所有API请求和响应
- 敏感信息自动脱敏
- 包含请求时间和响应时间

---

**注意**: 所有API密钥都应该安全存储，不要在客户端代码中硬编码。
