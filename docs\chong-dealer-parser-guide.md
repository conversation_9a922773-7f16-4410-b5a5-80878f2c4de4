# Chong Dealer 订单解析器使用指南

## 📋 概述

基于对 `_chat.txt` 文件的深入分析，我们开发了专门针对 Chong Dealer OTA 平台的订单解析器。该解析器能够准确识别和解析来自 Chong Dealer 平台的各种订单格式，包括单程订单、多程订单和特殊服务订单。

## 🎯 主要功能

### 1. 多格式支持
- **标准详细格式**：包含完整字段的订单格式
- **简化格式**：精简的订单信息格式
- **多程订单**：同一客人的多个行程
- **特殊服务**：举牌、机场转乘、包车等

### 2. 智能识别
- **自动OTA类型检测**：根据文本特征自动识别平台类型
- **多订单分隔**：支持横线、数字编号、日期标题等分隔方式
- **字段智能提取**：使用正则表达式精确提取各类信息

### 3. 数据验证
- **必填字段检查**：确保关键信息完整
- **逻辑验证**：检查人数与车型匹配等业务规则
- **警告提示**：对可能的问题给出提醒

## 🚀 快速开始

### 安装和初始化

```javascript
const OrderParsingService = require('./src/services/OrderParsingService');

// 创建解析服务实例
const orderParsingService = new OrderParsingService();

// 初始化（可选：提供Logger）
orderParsingService.initialize({
    logger: console // 或自定义Logger
});
```

### 基本使用

```javascript
// 解析订单文本
const chatContent = `
用车地点：吉隆坡
用车时间：03月23日  17点，  3大人（7座）
客人姓名：黄子慧3人
航班号： CZ8301    广州白云T2 - 吉隆坡T1  12:25  17:00 
接机/送机：接机
接送地址：吉隆坡网红泳池吉隆坡市中心双子塔豪瑪酒店
`;

const result = orderParsingService.parseOrderText(chatContent);

if (result.success) {
    console.log(`解析成功，共 ${result.orders.length} 个订单`);
    console.log('订单详情：', result.orders);
} else {
    console.error('解析失败：', result.error);
}
```

## 📊 支持的订单格式

### 1. 标准详细格式

```
接送机类型：接机
姓名：刘思雨
航班号：CA871
用车时间：3.2晚11点30
接客地址：吉隆坡机场T1
送客地址：One Residences @ Chan Sow Lin
乘客人数：4
行李数量：3
联系方式：15296989128
微信号：Rain14321
```

### 2. 简化格式

```
3.3接机：D7321 19.45抵达 
3.5送机：OD1900 08.00起飞 

姓名：袁媛
人数：2
车型：经济五座
酒店：吉隆坡希尔顿逸林酒店
结算：75+75+30
```

### 3. 多程订单

```
3月2日：吉隆坡接机
D7343, 08:50抵达

3月4日：吉隆坡送机
AK5740，18:15起飞，15：00接客人

客人：3人 周怡/于秋浩 15727366883 
车型：5座车
酒店：菲斯2期酒店
结算：75*2
```

### 4. 特殊服务

```
3月11日：吉隆坡接机+举牌
MH763  21:20抵达

机场转乘：吉隆坡T2换T1 点对点
包车5小时：市区观光
```

## 🔧 高级功能

### 订单验证

```javascript
// 验证解析后的订单
const validationResult = orderParsingService.validateOrders(result.orders);

console.log(`有效订单：${validationResult.valid.length}`);
console.log(`无效订单：${validationResult.invalid.length}`);
console.log(`警告订单：${validationResult.warnings.length}`);

// 查看具体的验证错误
validationResult.invalid.forEach(item => {
    console.log('错误：', item.errors);
});
```

### GoMyHire格式转换

```javascript
// 转换为GoMyHire API格式
const goMyHireOrders = orderParsingService.processOrdersForGoMyHire(result.orders);

// 发送到GoMyHire API
goMyHireOrders.forEach(order => {
    // 调用GoMyHire API
    console.log('GoMyHire订单：', order);
});
```

### 自定义解析器

```javascript
// 添加新的OTA解析器
const customParser = new CustomOTAParser();
const detectionPatterns = [/CustomOTA/, /特定格式/];

orderParsingService.addParser('custom_ota', customParser, detectionPatterns);
```

## 📈 性能优化

### 批量处理

```javascript
// 处理大量订单文本
const largeTextArray = [...]; // 多个聊天记录

const results = largeTextArray.map(text => 
    orderParsingService.parseOrderText(text)
);

// 合并所有订单
const allOrders = results
    .filter(r => r.success)
    .flatMap(r => r.orders);
```

### 缓存配置

```javascript
// 在配置文件中启用缓存
{
  "performance": {
    "enableCaching": true,
    "cacheExpiryMinutes": 60,
    "maxTextLength": 1000000
  }
}
```

## 🛠️ 配置选项

### OTA平台配置

编辑 `config/ota-parsing-rules.json` 文件：

```json
{
  "otaPlatforms": {
    "chong_dealer": {
      "enabled": true,
      "priority": 1,
      "detectionPatterns": [
        "CHONG 车头",
        "用车地点[:：]",
        "\\*京鱼\\*"
      ],
      "fieldMappings": {
        "customerName": ["姓名", "客人姓名", "客人"],
        "serviceType": ["接送机类型", "服务类型"]
      }
    }
  }
}
```

### 智能选择规则

```json
{
  "smartSelection": {
    "enabled": true,
    "rules": {
      "carTypeByPassengerCount": {
        "1-4": "Compact 5 Seater",
        "5-7": "Comfort 7 Seater",
        "8-10": "Premium 10 Seater"
      }
    }
  }
}
```

## 🧪 测试

### 运行测试

```bash
# 运行解析器测试
node src/parsers/ChongDealerOrderParser.test.js

# 运行集成演示
node examples/chong-dealer-integration.js
```

### 自定义测试

```javascript
const { testChongDealerParser } = require('./src/parsers/ChongDealerOrderParser.test.js');

// 运行测试
testChongDealerParser();
```

## 📝 最佳实践

### 1. 错误处理

```javascript
try {
    const result = orderParsingService.parseOrderText(chatContent);
    if (!result.success) {
        // 记录错误并提供备选方案
        logger.error('订单解析失败', { error: result.error });
        // 可以尝试手动解析或提示用户
    }
} catch (error) {
    logger.error('解析器异常', { error: error.message });
}
```

### 2. 数据清理

```javascript
// 预处理聊天记录
const cleanedContent = chatContent
    .replace(/\[.*?\]/g, '')  // 移除时间戳
    .replace(/‎/g, '')        // 移除不可见字符
    .trim();

const result = orderParsingService.parseOrderText(cleanedContent);
```

### 3. 结果验证

```javascript
// 总是验证解析结果
const validationResult = orderParsingService.validateOrders(result.orders);

// 只处理有效订单
const validOrders = validationResult.valid;

// 记录警告信息
validationResult.warnings.forEach(warning => {
    logger.warn('订单警告', warning);
});
```

## 🔍 故障排除

### 常见问题

1. **解析结果为空**
   - 检查文本是否包含Chong Dealer特征
   - 验证订单格式是否符合预期

2. **字段提取不准确**
   - 检查正则表达式是否匹配
   - 调整字段映射配置

3. **多订单分隔失败**
   - 确认分隔符格式
   - 检查订单边界识别逻辑

### 调试技巧

```javascript
// 启用详细日志
const logger = {
    info: (msg, data) => console.log(`[INFO] ${msg}`, data),
    warn: (msg, data) => console.warn(`[WARN] ${msg}`, data),
    error: (msg, data) => console.error(`[ERROR] ${msg}`, data)
};

orderParsingService.initialize({ logger });
```

## 📞 支持

如有问题或建议，请：

1. 查看测试用例了解支持的格式
2. 检查配置文件设置
3. 查看日志输出获取详细信息
4. 参考示例代码进行集成

---

*该解析器基于实际的Chong Dealer聊天记录分析开发，持续优化中。*
