# 开发文档

## 项目架构

### 系统概述

OTA订单处理系统采用纯前端架构，所有逻辑在浏览器中执行，通过API与外部服务集成。

### 架构原则

1. **模块化设计**: 功能模块独立，低耦合高内聚
2. **API优先**: 通过标准API与外部服务集成
3. **响应式设计**: 适配不同设备和屏幕尺寸
4. **渐进增强**: 基础功能优先，高级功能可选

### 目录结构

```
/
├── index.html              # 主页面
├── README.md              # 项目说明
├── core/                  # 核心模块
│   ├── app.js            # 主应用入口
│   ├── config.js         # 统一配置
│   ├── logger.js         # 日志系统
│   └── prompts.js        # AI提示词配置
├── services/              # 业务服务
│   ├── api-service.js    # API调用服务
│   ├── llm-service.js    # LLM处理服务
│   ├── order-parser.js   # 订单解析服务
│   └── image-service.js  # 图像处理服务
├── assets/               # 静态资源
│   ├── styles.css       # 主样式文件
│   └── logger.css       # 日志样式文件
├── docs/                 # 文档
│   ├── user-guide.md    # 用户指南
│   ├── api-reference.md # API参考
│   └── development.md   # 开发文档
└── data/                 # 数据文件
    └── test-orders.txt  # 测试数据
```

## 核心模块

### 1. 应用状态管理 (AppState)

负责管理应用的全局状态，包括用户认证、系统数据缓存等。

```javascript
class AppState {
    constructor() {
        this.token = localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.TOKEN);
        this.userInfo = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.USER_INFO) || 'null');
        this.backendUsers = [];
        this.subCategories = [];
        this.carTypes = [];
        this.processedOrders = [];
    }
}
```

### 2. API服务 (ApiService)

封装所有GoMyHire API调用，提供统一的接口。

```javascript
class ApiService {
    async login(email, password) { /* 用户登录 */ }
    async getBackendUsers() { /* 获取后台用户 */ }
    async getSubCategories() { /* 获取子分类 */ }
    async getCarTypes() { /* 获取车型 */ }
    async createOrder(orderData) { /* 创建订单 */ }
}
```

### 3. LLM服务 (LLMService)

管理DeepSeek和Gemini AI服务，提供智能订单处理。

```javascript
class LLMService {
    async processOrderText(text, otaType) { /* 处理订单文本 */ }
    async checkDeepSeekConnection() { /* 检测DeepSeek连接 */ }
    async checkGeminiConnection() { /* 检测Gemini连接 */ }
}
```

### 4. 订单解析器 (OrderParser)

整合本地关键词检测和LLM处理的订单解析服务。

```javascript
class OrderParser {
    async parseOrders(text, otaType) { /* 解析订单 */ }
    detectOtaType(text) { /* 检测OTA类型 */ }
    parseChongDealerOrders(text) { /* Chong Dealer专用解析 */ }
}
```

### 5. 图像服务 (ImageService)

处理图片上传、OCR识别和图像分析功能。

```javascript
class ImageService {
    async processImageFiles(files) { /* 处理图片文件 */ }
    async performOCR(base64Data) { /* OCR文字识别 */ }
    async extractOrdersFromImages(imageResults) { /* 从图片提取订单 */ }
}
```

## 配置管理

### 系统配置 (SYSTEM_CONFIG)

所有配置信息集中在 `core/config.js` 中：

```javascript
const SYSTEM_CONFIG = {
    // 系统信息
    SYSTEM_INFO: {
        name: 'OTA订单处理系统',
        version: '2.0.0'
    },
    
    // API配置
    API: {
        BASE_URL: 'https://staging.gomyhire.com.my/api',
        DEEPSEEK: { /* DeepSeek配置 */ },
        GEMINI: { /* Gemini配置 */ },
        GOOGLE_VISION: { /* Google Vision配置 */ }
    },
    
    // OTA类型配置
    OTA_TYPES: { /* OTA检测规则 */ },
    
    // 智能选择规则
    SMART_SELECTION: { /* 智能选择配置 */ }
};
```

## 开发规范

### 1. 代码规范

#### 命名规范
- **类名**: PascalCase (如: `ApiService`)
- **函数名**: camelCase (如: `processOrder`)
- **变量名**: camelCase (如: `orderData`)
- **常量名**: UPPER_SNAKE_CASE (如: `API_BASE_URL`)

#### 注释规范
```javascript
/**
 * @function functionName - 功能描述
 * @param {type} paramName - 参数描述
 * @returns {type} 返回值描述
 */
function functionName(paramName) {
    // 实现逻辑
}
```

#### 文件头注释
```javascript
/**
 * @file filename.js - 文件功能描述
 * @description 详细描述文件的作用和功能
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 */
```

### 2. 错误处理

#### 统一错误处理模式
```javascript
try {
    const result = await someAsyncOperation();
    logger.success('操作', '操作成功', { result });
    return result;
} catch (error) {
    logger.error('操作', '操作失败', { error: error.message });
    throw new Error(`操作失败: ${error.message}`);
}
```

#### 错误分类
- **网络错误**: API调用失败
- **验证错误**: 数据格式不正确
- **业务错误**: 业务逻辑错误
- **系统错误**: 系统级别错误

### 3. 日志规范

#### 日志级别
- **DEBUG**: 调试信息
- **INFO**: 一般信息
- **WARN**: 警告信息
- **ERROR**: 错误信息
- **SUCCESS**: 成功信息

#### 日志格式
```javascript
logger.info('模块名', '操作描述', {
    key1: 'value1',
    key2: 'value2'
});
```

## 测试指南

### 1. 功能测试

#### 登录测试
```javascript
// 测试用例
const testCases = [
    {
        name: '正常登录',
        email: '<EMAIL>',
        password: '1234',
        expected: 'success'
    },
    {
        name: '错误密码',
        email: '<EMAIL>',
        password: 'wrong',
        expected: 'error'
    }
];
```

#### 订单处理测试
```javascript
// 测试数据
const testOrderText = `
1.28接机：KE671 22.20抵达
联系人：张梦媛
人数：6
酒店：Santa Grand Signature
`;
```

### 2. 性能测试

#### 响应时间要求
- **登录**: < 3秒
- **订单处理**: < 20秒
- **图片OCR**: < 15秒
- **订单创建**: < 5秒

### 3. 兼容性测试

#### 浏览器支持
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 部署指南

### 1. 本地开发

```bash
# 启动本地服务器
python -m http.server 8000

# 或使用Node.js
npx http-server

# 访问地址
http://localhost:8000
```

### 2. 生产部署

#### 静态文件部署
1. 将所有文件上传到Web服务器
2. 确保支持HTTPS
3. 配置正确的MIME类型
4. 启用Gzip压缩

#### CDN部署
1. 上传静态资源到CDN
2. 更新资源引用路径
3. 配置缓存策略
4. 启用HTTP/2

### 3. 环境配置

#### API密钥配置
```javascript
// 在config.js中配置
const SYSTEM_CONFIG = {
    API: {
        DEEPSEEK: {
            API_KEY: 'your-deepseek-api-key'
        },
        GEMINI: {
            API_KEY: 'your-gemini-api-key'
        },
        GOOGLE_VISION: {
            API_KEY: 'your-google-vision-api-key'
        }
    }
};
```

## 故障排除

### 1. 常见问题

#### API调用失败
- 检查网络连接
- 验证API密钥
- 查看控制台错误
- 检查CORS设置

#### 图片识别失败
- 确认图片格式支持
- 检查文件大小限制
- 验证Google Vision API配置
- 查看API配额使用情况

### 2. 调试工具

#### 浏览器开发者工具
- Console: 查看错误信息
- Network: 检查API请求
- Application: 查看本地存储
- Sources: 调试JavaScript代码

#### 日志系统
- 内置调试控制台
- 可过滤日志级别
- 支持导出日志
- API请求详细记录

---

**注意**: 开发过程中请遵循代码规范，确保代码质量和可维护性。
