# OTA订单处理系统 - 用户指南

## 系统概述

OTA订单处理系统是一个智能化的订单处理平台，支持文字和图片输入，使用AI自动处理订单信息，并通过API创建订单。

### 主要功能

- **多种输入方式**：支持文字输入和图片上传
- **智能AI处理**：DeepSeek主要LLM + Gemini备用LLM
- **OTA类型识别**：自动识别不同OTA平台的订单格式
- **订单智能解析**：提取客人信息、航班信息、服务类型等
- **批量订单创建**：自动调用GoMyHire API创建订单

## 快速开始

### 1. 系统登录

1. 打开系统后会自动弹出登录窗口
2. 输入登录信息：
   - 邮箱：`<EMAIL>`
   - 密码：`1234`
3. 点击"登录"按钮

### 2. 文字订单处理

#### 输入订单文本
1. 选择"文字输入"标签
2. 在文本框中输入订单信息，例如：
```
1.28接机：KE671 22.20抵达
1.30送机：AK378 16.20起飞

联系人：张梦媛
人数：6
车型：商务十座
酒店：Santa Grand Signature

JY
```

#### 选择OTA类型
- **自动识别**：系统自动检测订单类型（推荐）
- **Chong Dealer**：专门处理Chong Dealer平台订单
- **其他**：通用订单处理

#### 处理订单
1. 点击"处理订单"按钮
2. 系统会使用AI分析订单内容
3. 处理完成后显示结果预览

### 3. 图片订单处理

#### 上传图片
1. 选择"图片上传"标签
2. 点击上传区域或直接拖拽图片文件
3. 支持多张图片同时上传
4. 支持格式：JPG, PNG, GIF, WebP

#### 自动处理
1. 系统会自动进行OCR文字识别
2. 提取图片中的订单信息
3. 自动填入文本框并处理

### 4. 结果编辑与确认

#### 查看处理结果
- 在结果预览区域查看解析的订单信息
- 检查客人姓名、航班信息、服务类型等

#### 手动编辑
1. 点击"编辑结果"按钮
2. 修改不正确的信息
3. 保存修改

#### 重新处理
- 如果结果不满意，可以点击"重新处理"

### 5. 创建订单

#### 确认信息
1. 确认所有订单信息正确
2. 检查必填字段是否完整

#### 批量创建
1. 点击"创建订单"按钮
2. 系统会自动调用GoMyHire API
3. 显示每个订单的创建状态

## 订单处理规则

### 日期处理
- 自动修正过期日期
- 智能推算未来日期
- 处理跨月跨年情况
- 结果默认为2025年

### 时间计算
- **接机**：使用航班到达时间
- **送机**：航班起飞时间减去3.5小时

### 地点处理
- **接机**：pickup = klia, drop = 酒店英文名
- **送机**：pickup = 酒店英文名, drop = klia
- 自动转换中文酒店名为英文

### 参考号生成
- 基于日期+时间+航班号+客人姓名
- **通用格式**：`202402141422-AK5747-余欣怡`
- **Chong Dealer格式**：`202402141422-AK5747-余欣怡-CHONG`

### 举牌服务处理
- 自动识别举牌服务关键词
- 为接机订单自动生成独立的举牌服务订单
- 自动提取客人姓名用于举牌服务

## 智能选择功能

### 车型自动选择
系统根据乘客人数自动选择合适的车型：
- 1-4人：Comfort 5 Seater
- 5-7人：Premium 7 Seater
- 8-10人：Luxury 10 Seater

### 用户自动分配
根据OTA类型自动分配后台用户：
- Chong Dealer：分配给专门的处理人员
- 其他类型：分配给默认用户

### 分类自动选择
根据服务类型自动选择子分类：
- 接机：Airport Pickup
- 送机：Airport Drop-off
- 包车：Charter Service

## 状态指示器

### LLM服务状态
- **绿色**：服务正常连接
- **黄色**：正在检测连接
- **红色**：连接失败

### 处理状态
- **处理中**：显示进度条和状态信息
- **成功**：显示成功图标和结果
- **失败**：显示错误信息和建议

## 常见问题

### 1. 登录失败
- 检查网络连接
- 确认登录凭据正确
- 查看控制台错误信息

### 2. AI处理失败
- 检查API密钥配置
- 确认网络连接正常
- 尝试重新处理

### 3. 订单创建失败
- 检查必填字段是否完整
- 确认用户权限
- 查看API返回的错误信息

### 4. 图片识别效果差
- 确保图片清晰度足够
- 避免模糊或倾斜的图片
- 检查文字是否清晰可读

## 系统要求

### 浏览器支持
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### 网络要求
- 稳定的互联网连接
- 支持HTTPS协议
- 能够访问外部API服务

### 文件要求
- 图片格式：JPG, PNG, GIF, WebP
- 单个文件大小：最大10MB
- 同时上传：最多10个文件

## 技术支持

如遇到问题，请：
1. 查看浏览器控制台错误信息
2. 检查网络连接状态
3. 确认API配置正确
4. 联系技术支持团队

---

**注意**：本系统仅用于演示和学习目的。在生产环境中使用前，请确保进行充分的测试和安全评估。
