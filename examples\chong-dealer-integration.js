/**
 * @file chong-dealer-integration.js - Chong Dealer集成示例
 * @description 展示如何在OTA订单处理系统中集成Chong Dealer解析器
 */

const OrderParsingService = require('../src/services/OrderParsingService');
const fs = require('fs');
const path = require('path');

/**
 * @function demonstrateChongDealerIntegration - 演示Chong Dealer集成
 */
async function demonstrateChongDealerIntegration() {
    console.log('🚀 Chong Dealer订单解析器集成演示\n');
    
    // 1. 初始化订单解析服务
    const orderParsingService = new OrderParsingService();
    
    // 模拟Logger
    const mockLogger = {
        info: (msg, data) => console.log(`ℹ️  ${msg}`, data || ''),
        warn: (msg, data) => console.log(`⚠️  ${msg}`, data || ''),
        error: (msg, data) => console.log(`❌ ${msg}`, data || '')
    };
    
    orderParsingService.initialize({ logger: mockLogger });
    
    // 2. 读取实际的聊天记录文件
    let chatContent = '';
    try {
        chatContent = fs.readFileSync(path.join(__dirname, '../_chat.txt'), 'utf8');
        console.log(`📄 成功读取聊天记录文件，长度: ${chatContent.length} 字符\n`);
    } catch (error) {
        console.log('⚠️  无法读取_chat.txt文件，使用示例数据\n');
        chatContent = getSampleChatContent();
    }
    
    // 3. 解析订单
    console.log('🔍 开始解析订单...\n');
    const parseResult = orderParsingService.parseOrderText(chatContent);
    
    if (parseResult.success) {
        console.log(`✅ 解析成功！`);
        console.log(`📊 统计信息:`);
        console.log(`   - OTA类型: ${parseResult.otaType}`);
        console.log(`   - 订单数量: ${parseResult.orders.length}`);
        console.log(`   - 文本长度: ${parseResult.metadata.textLength} 字符\n`);
        
        // 4. 验证订单
        console.log('🔍 验证订单数据...\n');
        const validationResult = orderParsingService.validateOrders(parseResult.orders);
        
        console.log(`📈 验证结果:`);
        console.log(`   - 有效订单: ${validationResult.valid.length}`);
        console.log(`   - 无效订单: ${validationResult.invalid.length}`);
        console.log(`   - 警告订单: ${validationResult.warnings.length}\n`);
        
        // 5. 显示前几个订单的详细信息
        console.log('📋 订单详情示例:\n');
        parseResult.orders.slice(0, 3).forEach((order, index) => {
            console.log(`订单 ${index + 1}:`);
            console.log(`   客人: ${order.customerName || '未知'}`);
            console.log(`   服务: ${order.serviceType || '未知'}`);
            console.log(`   日期: ${order.serviceDate || '未知'}`);
            console.log(`   航班: ${order.flightNumber || '无'}`);
            console.log(`   人数: ${order.passengerCount}人`);
            console.log(`   车型: ${order.carType || '未指定'} ${order.seatCount}座`);
            console.log(`   酒店: ${order.hotel || '未指定'}`);
            console.log(`   特殊服务: ${order.specialServices.join(', ') || '无'}`);
            console.log(`   状态: ${order.status}\n`);
        });
        
        // 6. 转换为GoMyHire格式
        console.log('🔄 转换为GoMyHire格式...\n');
        const goMyHireOrders = orderParsingService.processOrdersForGoMyHire(parseResult.orders);
        
        console.log(`📤 GoMyHire格式订单示例:`);
        goMyHireOrders.slice(0, 2).forEach((order, index) => {
            console.log(`GoMyHire订单 ${index + 1}:`);
            console.log(`   customerName: ${order.customerName}`);
            console.log(`   serviceType: ${order.serviceType}`);
            console.log(`   carType: ${order.carType}`);
            console.log(`   pickupAddress: ${order.pickupAddress}`);
            console.log(`   dropoffAddress: ${order.dropoffAddress}\n`);
        });
        
        // 7. 显示错误和警告
        if (validationResult.invalid.length > 0) {
            console.log('❌ 无效订单:');
            validationResult.invalid.forEach((item, index) => {
                console.log(`   ${index + 1}. 错误: ${item.errors.join(', ')}`);
            });
            console.log('');
        }
        
        if (validationResult.warnings.length > 0) {
            console.log('⚠️  警告订单:');
            validationResult.warnings.forEach((item, index) => {
                console.log(`   ${index + 1}. 警告: ${item.warnings.join(', ')}`);
            });
            console.log('');
        }
        
        // 8. 生成处理报告
        generateProcessingReport(parseResult, validationResult);
        
    } else {
        console.log(`❌ 解析失败: ${parseResult.error}`);
    }
}

/**
 * @function getSampleChatContent - 获取示例聊天内容
 * @returns {string} 示例聊天内容
 */
function getSampleChatContent() {
    return `
[2024/2/28 15:59:13] CHONG 车头: 用车地点：吉隆坡
用车时间：03月23日  17点，  3大人（7座）
客人姓名：黄子慧3人
航班号： CZ8301    广州白云T2 - 吉隆坡T1  12:25  17:00 
接机/送机：接机
接送地址：吉隆坡网红泳池吉隆坡市中心双子塔豪瑪酒店(The Platinum 2 Kuala Lumpur by Holma)
-------
用车时间：03月25日   3点半左右，3大人（7座）
客人姓名：黄子慧3人 
航班号：AK6224    吉隆坡 - 登嘉楼 07:30  08:30 
接机/送机：送机
接送地址：吉隆坡网红泳池吉隆坡市中心双子塔豪瑪酒店(The Platinum 2 Kuala Lumpur by Holma)

*京鱼*

[2024/2/29 12:55:53] CHONG 车头: 赵小涵   1人 5座

吉隆坡接送机：

1. MF823   03月01日  厦门高崎 - 吉隆坡  20:05  00:10+1   
2. MF824   03月06日  吉隆坡 - 厦门高崎  08:00  12:05   
酒店：3.1-3.6吉隆坡斯特格酒店(STEG Kuala Lumpur)

*京鱼*

[2024/2/29 16:27:37] CHONG 车头: 接送机类型：接机
姓名：刘思雨
航班号：CA871
用车时间：3.2晚11点30
接客地址：吉隆坡机场T1
送客地址：One Residences @ Chan Sow Lin by Birdy Stay
乘客人数：4
行李数量:  3
联系方式：15296989128
微信号：Rain14321

*迹象*

3.3接机：D7321 19.45抵达 
3.5送机：OD1900 08.00起飞 

姓名：袁媛
人数：2
车型：经济五座
酒店：吉隆坡希尔顿逸林酒店
结算：75+75+30

*京鱼*
`;
}

/**
 * @function generateProcessingReport - 生成处理报告
 * @param {Object} parseResult - 解析结果
 * @param {Object} validationResult - 验证结果
 */
function generateProcessingReport(parseResult, validationResult) {
    console.log('📊 处理报告:\n');
    
    const report = {
        timestamp: new Date().toISOString(),
        otaType: parseResult.otaType,
        totalOrders: parseResult.orders.length,
        validOrders: validationResult.valid.length,
        invalidOrders: validationResult.invalid.length,
        warningOrders: validationResult.warnings.length,
        successRate: ((validationResult.valid.length / parseResult.orders.length) * 100).toFixed(2),
        
        // 统计信息
        serviceTypes: {},
        carTypes: {},
        customerCount: new Set(parseResult.orders.map(o => o.customerName)).size,
        
        // 常见问题
        commonIssues: []
    };
    
    // 统计服务类型
    parseResult.orders.forEach(order => {
        const serviceType = order.serviceType || '未知';
        report.serviceTypes[serviceType] = (report.serviceTypes[serviceType] || 0) + 1;
        
        const carType = order.carType || '未指定';
        report.carTypes[carType] = (report.carTypes[carType] || 0) + 1;
    });
    
    // 收集常见问题
    validationResult.invalid.forEach(item => {
        item.errors.forEach(error => {
            if (!report.commonIssues.includes(error)) {
                report.commonIssues.push(error);
            }
        });
    });
    
    console.log(`总订单数: ${report.totalOrders}`);
    console.log(`有效订单: ${report.validOrders} (${report.successRate}%)`);
    console.log(`客人数量: ${report.customerCount}`);
    console.log(`服务类型分布:`, report.serviceTypes);
    console.log(`车型分布:`, report.carTypes);
    
    if (report.commonIssues.length > 0) {
        console.log(`常见问题:`, report.commonIssues);
    }
    
    console.log('\n✅ 集成演示完成！');
}

// 运行演示
if (require.main === module) {
    demonstrateChongDealerIntegration().catch(console.error);
}

module.exports = {
    demonstrateChongDealerIntegration,
    getSampleChatContent
};
`;
