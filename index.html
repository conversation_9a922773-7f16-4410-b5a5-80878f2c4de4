<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA订单处理系统</title>
    <link rel="stylesheet" href="assets/styles.css">
    <link rel="stylesheet" href="assets/logger.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <!-- 登录窗口 -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <h2>系统登录</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">邮箱:</label>
                    <input type="email" id="email" value="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" value="1234" required>
                </div>
                <button type="submit">登录</button>
                <div id="loginError" class="error-message"></div>
            </form>
        </div>
    </div>

    <!-- 主界面 -->
    <div id="mainApp" class="hidden">
        <header>
            <h1>OTA订单处理系统</h1>
            <div class="header-controls">
                <!-- DeepSeek 连接状态指示器 (主要LLM) -->
                <div class="llm-status-indicator deepseek-indicator" id="deepseekStatusIndicator" title="点击检测 DeepSeek API 连接状态">
                    <div class="status-light" id="deepseekStatusLight"></div>
                    <span class="status-text" id="deepseekStatusText">DeepSeek 检测中...</span>
                    <span class="llm-label">主要</span>
                </div>

                <!-- Gemini 连接状态指示器 (后备LLM) -->
                <div class="llm-status-indicator gemini-indicator" id="geminiStatusIndicator" title="点击检测 Gemini API 连接状态">
                    <div class="status-light" id="geminiStatusLight"></div>
                    <span class="status-text" id="geminiStatusText">Gemini 检测中...</span>
                    <span class="llm-label">后备</span>
                </div>

                <div class="user-info">
                    <span id="userInfo"></span>
                    <button id="logoutBtn">退出登录</button>
                </div>
            </div>
        </header>

        <main>
            <!-- 订单输入区域 -->
            <section id="orderInput" class="section">
                <h2>订单内容输入</h2>
                <div class="input-tabs">
                    <button class="tab-btn active" data-tab="text">文字输入</button>
                    <button class="tab-btn" data-tab="image">图片上传</button>
                </div>

                <!-- 文字输入 -->
                <div id="textInput" class="tab-content active">
                    <textarea id="orderText" placeholder="请输入订单内容...\n\n示例：\n1.28接机：KE671 22.20抵达\n1.30送机：AK378 16.20起飞\n\n联系人：张梦媛\n人数：6\n车型：商务十座\n酒店：Santa Grand Signature\n\nJY"></textarea>
                </div>

                <!-- 图片上传 -->
                <div id="imageInput" class="tab-content">
                    <div class="upload-area" id="uploadArea">
                        <p>点击或拖拽图片到此处上传</p>
                        <input type="file" id="imageFile" accept="image/*" multiple title="选择要处理的图片文件">
                    </div>
                    <div id="imagePreview" class="image-preview"></div>
                    
                    <!-- 图片分析结果预览 -->
                    <div id="imageAnalysisResult" class="image-analysis-result hidden">
                        <h3>图片分析结果</h3>
                        <div class="analysis-tabs">
                            <button class="analysis-tab-btn active" data-analysis-tab="text">提取文字</button>
                            <button class="analysis-tab-btn" data-analysis-tab="details">详细分析</button>
                        </div>
                        <div id="extractedText" class="analysis-content active">
                            <textarea id="extractedTextContent" readonly placeholder="从图片中提取的文字将显示在这里..."></textarea>
                        </div>
                        <div id="analysisDetails" class="analysis-content">
                            <div id="imageLabels" class="analysis-section">
                                <h4>图片标签</h4>
                                <div id="labelsList"></div>
                            </div>
                            <div id="imageObjects" class="analysis-section">
                                <h4>检测到的物体</h4>
                                <div id="objectsList"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- OTA选择 -->
                <div class="ota-selection">
                    <label for="otaSelect">选择OTA类型:</label>
                    <select id="otaSelect">
                        <option value="auto">自动识别</option>
                        <option value="chong-dealer">Chong Dealer</option>
                        <option value="other">其他</option>
                    </select>
                </div>

                <button id="processBtn" class="primary-btn">处理订单</button>
            </section>

            <!-- 处理结果预览 -->
            <section id="resultPreview" class="section hidden">
                <h2>处理结果预览</h2>
                <div class="result-controls">
                    <button id="editBtn">编辑结果</button>
                    <button id="refreshBtn">重新处理</button>
                </div>
                <div id="resultContent" class="result-content">
                    <!-- 订单处理结果 -->
                    <div id="orderResults"></div>

                    <!-- 图片处理结果 -->
                    <div id="imageResults"></div>
                </div>
                <div class="action-buttons">
                    <button id="createOrderBtn" class="primary-btn">创建订单</button>
                    <button id="exportBtn">导出结果</button>
                </div>
            </section>

            <!-- 订单创建状态 -->
            <section id="orderStatus" class="section hidden">
                <h2>订单创建状态</h2>
                <div id="statusContent">
                    <!-- 创建结果 -->
                    <div id="createResults"></div>
                </div>
            </section>
        </main>
    </div>

    <!-- 加载提示 -->
    <div id="loadingModal" class="modal hidden">
        <div class="modal-content">
            <div class="loading-spinner"></div>
            <p id="loadingText">正在处理...</p>
        </div>
    </div>

    <!-- 引入核心模块 -->
    <script src="core/config.js"></script>
    <script src="core/prompts.js"></script>
    <script src="core/logger.js"></script>

    <!-- 引入服务模块 -->
    <script src="services/api-service.js"></script>
    <script src="services/llm-service.js"></script>
    <script src="services/order-parser.js"></script>
    <script src="services/image-service.js"></script>

    <!-- 引入主应用 -->
    <script src="core/app.js"></script>
</body>
</html>