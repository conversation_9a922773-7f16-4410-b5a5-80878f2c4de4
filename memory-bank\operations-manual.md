# OTA订单处理系统 - 运维操作手册

## 部署指南

### 1. 系统要求

#### 1.1 运行环境
- **操作系统**: Windows 10+, macOS 10.15+, Linux
- **浏览器**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **网络**: 稳定的互联网连接（用于API调用）
- **存储**: 至少100MB可用空间

#### 1.2 API服务要求
- **Google Vision API**: 有效的API密钥和配额
- **DeepSeek API**: 有效的API密钥和配额
- **Gemini API**: 有效的API密钥和配额
- **GoMyHire API**: 有效的登录凭据

### 2. 部署步骤

#### 2.1 文件部署
```bash
# 1. 下载项目文件
# 2. 解压到目标目录
# 3. 确保文件结构完整

项目结构:
├── index.html          # 主页面
├── styles.css          # 样式文件
├── app.js             # 主应用逻辑
├── config.js          # 配置文件
├── assets/            # 静态资源
├── docs/              # 文档
└── memory-bank/       # 项目文档
```

#### 2.2 配置API密钥
```javascript
// 编辑 config.js 文件
const CONFIG = {
    // Google Vision API配置
    GOOGLE_VISION: {
        API_KEY: 'your-google-vision-api-key-here',
        // ... 其他配置
    },
    
    // DeepSeek API配置
    DEEPSEEK: {
        API_KEY: 'your-deepseek-api-key-here',
        // ... 其他配置
    },
    
    // Gemini API配置
    GEMINI: {
        API_KEY: 'your-gemini-api-key-here',
        // ... 其他配置
    }
};
```

#### 2.3 部署选项

**选项1: 本地文件系统**
```bash
# 直接在浏览器中打开 index.html
file:///path/to/project/index.html
```

**选项2: 简单HTTP服务器**
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000

# Node.js
npx http-server

# 访问: http://localhost:8000
```

**选项3: 静态网站托管**
- GitHub Pages
- Netlify
- Vercel
- AWS S3 + CloudFront

### 3. 配置管理

#### 3.1 API密钥获取

**Google Vision API**
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Vision API
4. 创建服务账号密钥
5. 复制API密钥到配置文件

**DeepSeek API**
1. 访问 [DeepSeek平台](https://platform.deepseek.com/)
2. 注册账号并登录
3. 进入API密钥管理页面
4. 创建新的API密钥
5. 复制密钥到配置文件

**Gemini API**
1. 访问 [Google AI Studio](https://makersuite.google.com/)
2. 登录Google账号
3. 创建API密钥
4. 复制密钥到配置文件

#### 3.2 环境配置
```javascript
// 开发环境配置
const DEV_CONFIG = {
    DEBUG_MODE: true,
    LOG_LEVEL: 'debug',
    API_TIMEOUT: 30000
};

// 生产环境配置
const PROD_CONFIG = {
    DEBUG_MODE: false,
    LOG_LEVEL: 'error',
    API_TIMEOUT: 15000
};
```

## 测试指南

### 1. 功能测试

#### 1.1 基础功能测试
```javascript
// 测试清单
□ 用户登录功能
□ 文字订单输入
□ 图片上传功能
□ Google Vision图像分析
□ DeepSeek/Gemini AI处理
□ 智能选择功能
□ 订单预览编辑
□ 批量订单创建
□ 状态指示器
□ 错误处理
```

#### 1.2 API连接测试
```javascript
// 使用测试页面验证
1. 打开 test-dual-llm.html
2. 测试DeepSeek连接
3. 测试Gemini连接
4. 测试Google Vision连接
5. 测试GoMyHire API连接
6. 验证故障转移机制
```

#### 1.3 智能选择测试
```javascript
// 测试场景
测试场景1: 小型接机订单
- 输入: 2人1件行李，接机服务
- 预期: Compact 5 Seater, Pickup分类, jcy1用户

测试场景2: 大型送机团队
- 输入: 8人6件行李，送机服务
- 预期: 10 Seater MPV, Dropoff分类, jcy1用户

测试场景3: 云顶包车服务
- 输入: 6人4件行李，KL到云顶
- 预期: Standard MPV, KL to genting分类, jcy1用户
```

### 2. 性能测试

#### 2.1 响应时间测试
```javascript
// 性能指标
- 页面加载时间: < 3秒
- API响应时间: < 5秒
- AI处理时间: < 30秒
- 图片上传时间: < 10秒
```

#### 2.2 并发测试
```javascript
// 并发场景
- 多用户同时登录
- 同时处理多个订单
- 大量图片同时上传
- API并发调用测试
```

### 3. 兼容性测试

#### 3.1 浏览器兼容性
```javascript
// 测试浏览器
□ Chrome 80+
□ Firefox 75+
□ Safari 13+
□ Edge 80+
□ 移动端浏览器
```

#### 3.2 设备兼容性
```javascript
// 测试设备
□ 桌面电脑 (1920x1080)
□ 笔记本电脑 (1366x768)
□ 平板电脑 (1024x768)
□ 手机 (375x667)
```

## 故障排除

### 1. 常见问题

#### 1.1 API连接问题

**问题**: DeepSeek连接失败
```javascript
解决方案:
1. 检查API密钥是否正确
2. 验证网络连接
3. 确认API配额是否充足
4. 检查API服务状态
5. 查看控制台错误信息
```

**问题**: Gemini连接失败
```javascript
解决方案:
1. 验证API密钥配置
2. 检查API服务区域限制
3. 确认请求格式正确
4. 检查API配额限制
5. 查看网络请求详情
```

**问题**: Google Vision分析失败
```javascript
解决方案:
1. 检查图片格式是否支持
2. 验证图片大小是否超限
3. 确认API密钥权限
4. 检查网络连接稳定性
5. 查看API响应错误
```

#### 1.2 功能问题

**问题**: 智能选择不准确
```javascript
解决方案:
1. 检查API数据是否最新
2. 验证选择算法逻辑
3. 查看选择过程日志
4. 确认订单数据完整性
5. 手动调整选择结果
```

**问题**: 订单创建失败
```javascript
解决方案:
1. 检查GoMyHire API连接
2. 验证订单数据格式
3. 确认必填字段完整
4. 检查用户权限
5. 查看API错误响应
```

### 2. 调试方法

#### 2.1 浏览器调试
```javascript
// 调试步骤
1. 打开浏览器开发者工具 (F12)
2. 查看Console标签的错误信息
3. 监控Network标签的API请求
4. 检查Application标签的本地存储
5. 使用Sources标签设置断点
```

#### 2.2 日志分析
```javascript
// 日志类型
- [INFO] 信息日志: 正常操作记录
- [ERROR] 错误日志: 异常和错误信息
- [API响应] API调用日志: 请求和响应详情
- [智能选择] 选择过程日志: 决策过程记录
```

#### 2.3 网络诊断
```javascript
// 网络检查
1. 测试基本网络连接
2. 检查DNS解析
3. 验证HTTPS证书
4. 测试API端点可达性
5. 检查防火墙设置
```

### 3. 错误代码

#### 3.1 API错误代码
```javascript
// Google Vision API
400: 请求格式错误
401: API密钥无效
403: 权限不足或配额超限
429: 请求频率过高
500: 服务器内部错误

// DeepSeek API
400: 请求参数错误
401: 认证失败
429: 速率限制
500: 服务器错误

// Gemini API
400: 请求格式错误
403: API密钥无效或配额不足
429: 请求过于频繁
500: 内部服务器错误

// GoMyHire API
401: 登录凭据无效
403: 权限不足
422: 数据验证失败
500: 服务器错误
```

#### 3.2 应用错误代码
```javascript
// 自定义错误代码
E001: 用户未登录
E002: 输入数据为空
E003: 图片格式不支持
E004: 文件大小超限
E005: AI处理超时
E006: 智能选择失败
E007: 订单验证失败
E008: 网络连接错误
```

## 安全指南

### 1. 数据安全

#### 1.1 敏感信息保护
```javascript
// 安全措施
1. API密钥加密存储
2. 用户密码不在前端保存
3. 订单数据临时存储
4. 定期清理本地数据
5. HTTPS强制传输
```

#### 1.2 访问控制
```javascript
// 访问控制策略
1. 用户身份验证
2. 会话超时管理
3. 权限级别控制
4. 操作日志记录
5. 异常行为监控
```

### 2. 系统安全

#### 2.1 输入验证
```javascript
// 验证规则
1. 文件类型检查
2. 文件大小限制
3. 输入内容过滤
4. SQL注入防护
5. XSS攻击防护
```

#### 2.2 网络安全
```javascript
// 网络安全措施
1. HTTPS加密传输
2. API请求签名
3. 跨域请求控制
4. 请求频率限制
5. 异常流量监控
```

## 性能优化

### 1. 前端优化

#### 1.1 资源优化
```javascript
// 优化策略
1. 代码压缩和混淆
2. 图片格式优化
3. 缓存策略配置
4. 懒加载实现
5. CDN加速使用
```

#### 1.2 渲染优化
```javascript
// 渲染优化
1. DOM操作优化
2. CSS动画硬件加速
3. 防抖和节流
4. 虚拟滚动
5. 组件懒加载
```

### 2. API优化

#### 2.1 请求优化
```javascript
// 请求优化
1. 请求合并
2. 并发控制
3. 超时设置
4. 重试机制
5. 缓存策略
```

#### 2.2 数据优化
```javascript
// 数据优化
1. 数据压缩
2. 分页加载
3. 增量更新
4. 本地缓存
5. 预加载策略
```

## 监控与维护

### 1. 系统监控

#### 1.1 性能监控
```javascript
// 监控指标
- 页面加载时间
- API响应时间
- 错误率统计
- 用户活跃度
- 系统资源使用
```

#### 1.2 业务监控
```javascript
// 业务指标
- 订单处理成功率
- AI处理准确率
- 用户满意度
- 功能使用频率
- 异常操作统计
```

### 2. 维护计划

#### 2.1 定期维护
```javascript
// 维护任务
□ 每日: 检查系统状态和错误日志
□ 每周: 更新API配额和性能报告
□ 每月: 系统备份和安全检查
□ 每季度: 功能更新和优化
□ 每年: 全面系统评估和升级
```

#### 2.2 应急响应
```javascript
// 应急流程
1. 问题识别和分类
2. 影响范围评估
3. 临时解决方案
4. 根本原因分析
5. 永久修复实施
6. 预防措施制定
```
