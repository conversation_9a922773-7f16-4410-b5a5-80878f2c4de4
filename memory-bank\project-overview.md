# OTA订单处理系统 - 项目概述

## 项目基本信息

- **项目名称**: OTA订单处理系统 (OTA Order Processing System)
- **项目类型**: 纯前端Web应用
- **开发语言**: HTML5, CSS3, JavaScript (ES6+)
- **创建日期**: 2024-12-19
- **最后更新**: 2025-01-02
- **开发者**: Auto IDE

## 项目目标

### 核心目标
创建一个智能化的OTA（Online Travel Agency）订单处理系统，支持文字和图片输入，使用AI自动处理订单信息，并通过API创建订单。

### 具体功能目标
1. **用户认证**: 安全的登录系统，持久化登录状态
2. **订单输入**: 支持文字输入和图片上传两种方式
3. **AI智能处理**: 使用Google Vision + DeepSeek/Gemini AI处理订单信息
4. **智能选择**: 基于订单内容自动选择车型、用户、分类
5. **结果预览**: 实时预览和编辑处理结果
6. **订单创建**: 通过GoMyHire API批量创建订单

## 业务背景

### 行业背景
OTA（Online Travel Agency）行业需要处理大量的旅游订单，包括机票、酒店、接送机等服务。传统的人工处理方式效率低下，容易出错，需要智能化的解决方案。

### 业务痛点
1. **手工处理效率低**: 人工处理订单信息耗时长，容易出错
2. **信息格式不统一**: 不同渠道的订单格式差异大，难以标准化
3. **重复工作多**: 大量重复的数据录入和格式转换工作
4. **错误率高**: 人工处理容易出现日期、时间、地点等信息错误
5. **处理速度慢**: 无法快速响应客户需求

### 解决方案价值
- **提升效率**: AI自动处理，大幅减少人工时间
- **降低错误**: 智能识别和校正，减少人为错误
- **标准化处理**: 统一的输出格式，便于后续处理
- **快速响应**: 实时处理，提升客户满意度

## 目标用户

### 主要用户群体

#### 1. 订单处理专员
- **角色描述**: 负责日常OTA订单处理的工作人员
- **技能水平**: 熟悉旅游业务，基础计算机操作技能
- **使用场景**: 每日处理50-200个订单
- **核心需求**: 快速、准确地处理订单信息

#### 2. 业务主管
- **角色描述**: 负责订单处理团队管理的主管
- **技能水平**: 丰富的业务经验，管理技能
- **使用场景**: 监控处理质量，处理异常订单
- **核心需求**: 系统稳定性，处理结果可控

#### 3. 客服人员
- **角色描述**: 处理客户咨询和投诉的客服人员
- **技能水平**: 客服技能，基础业务知识
- **使用场景**: 协助处理特殊订单，回答客户问题
- **核心需求**: 快速查询订单状态，处理结果透明

### 典型用户画像：张小美（订单处理专员）
- **年龄**: 25-35岁
- **工作经验**: 2-5年旅游行业经验
- **技术水平**: 熟练使用Office软件，基础网页操作
- **工作环境**: 办公室，使用台式机或笔记本
- **工作压力**: 每日处理大量订单，时间紧迫
- **期望**: 系统操作简单，处理速度快，结果准确

## 项目范围

### 已实现功能 ✅
- ✅ 用户登录/登出系统
- ✅ 文字订单输入
- ✅ 图片上传和Google Vision AI识别
- ✅ DeepSeek主要LLM + Gemini备用LLM
- ✅ 智能选择功能（车型、用户、分类）
- ✅ 举牌服务识别处理
- ✅ OTA参考号生成优化
- ✅ AI订单处理
- ✅ 结果预览和编辑
- ✅ 批量订单创建
- ✅ 状态反馈和错误处理
- ✅ Gemini连接状态提示灯

### 不包含功能 ❌
- ❌ 后端服务器
- ❌ 数据库存储
- ❌ 用户注册功能
- ❌ 订单历史记录
- ❌ 复杂的权限管理

## 技术约束

### 必须遵循的约束
1. **纯前端方案**: 不使用任何服务器，所有逻辑在浏览器中执行
2. **无外部依赖**: 不使用npm包管理，不引入第三方库（除必要的CDN）
3. **本地运行**: 支持直接在浏览器中打开HTML文件运行
4. **多AI服务**: Google Vision图像分析 + DeepSeek/Gemini文字处理
5. **API集成**: 与GoMyHire API进行订单创建

### 技术选择原因
- **纯前端**: 简化部署，降低维护成本
- **无依赖**: 提高系统稳定性，减少外部风险
- **多AI架构**: 专业化分工，提高处理准确性

## 业务规则

### OTA订单处理规则（基于Chong Dealer）

#### 1. 日期处理规则
- **过期日期修正**: 如果日期已过期，自动推算到下一年的对应日期
- **默认年份**: 处理结果默认为2025年
- **跨月处理**: 正确处理跨月跨年的日期
- **日期格式**: 统一输出为YYYY-MM-DD格式

#### 2. 时间计算规则
- **接机时间**: 使用航班到达时间
- **送机时间**: 航班起飞时间减去3.5小时
- **时间格式**: 24小时制，HH:MM格式

#### 3. 地点处理规则
- **接机**: pickup = "klia", drop = 酒店英文名
- **送机**: pickup = 酒店英文名, drop = "klia"
- **酒店名称**: 自动转换中文酒店名为标准英文名
- **地点标准化**: 统一地点名称格式

#### 4. 参考号生成规则
- **格式**: 日期时间-航班号-客人姓名
- **示例**: "202402141422-AK5747-余欣怡"
- **唯一性**: 确保每个订单的参考号唯一

#### 5. 智能选择规则
- **车型选择**: 根据乘客人数自动选择合适车型
- **用户选择**: 优先选择jcy1用户，支持Chong Dealer识别
- **分类选择**: 根据服务类型（接机/送机/包车）自动选择

## 成功标准

### 功能性标准
1. 用户能够成功登录系统
2. 支持文字和图片两种输入方式
3. AI能够准确处理订单信息（准确率 > 90%）
4. 能够成功创建订单到GoMyHire系统（成功率 > 95%）
5. 错误处理完善，用户体验良好

### 非功能性标准
1. **性能**: 页面加载时间 < 3秒，AI处理时间 < 30秒
2. **兼容性**: 支持主流浏览器（Chrome, Firefox, Safari, Edge）
3. **可用性**: 界面直观，操作简单，新用户5分钟内上手
4. **可靠性**: 错误处理完善，系统稳定，可用性 > 99%

## 风险与挑战

### 技术风险
1. **AI服务不稳定**: API可能出现服务中断
2. **处理准确率不达标**: AI识别错误率过高
3. **API兼容性问题**: GoMyHire API变更导致不兼容

### 业务风险
1. **数据丢失**: 无后端存储，刷新页面可能丢失数据
2. **并发处理**: 多用户同时使用可能导致冲突

### 缓解措施
1. 使用LocalStorage进行临时数据存储
2. 实现完善的错误处理和重试机制
3. 多AI服务备用机制（DeepSeek主要，Gemini备用）
4. 提供数据导出功能
5. 清晰的用户提示和操作指导

## 项目里程碑

### 第一阶段 - 基础框架 ✅
- [x] 项目结构搭建
- [x] 基础HTML页面
- [x] CSS样式设计
- [x] JavaScript框架

### 第二阶段 - 核心功能 ✅
- [x] 用户认证系统
- [x] 订单输入界面
- [x] AI集成
- [x] API调用

### 第三阶段 - 智能化增强 ✅
- [x] Google Vision图像分析
- [x] DeepSeek/Gemini双LLM架构
- [x] 智能选择功能
- [x] 举牌服务处理
- [x] 状态指示器

### 第四阶段 - 优化完善 🔄
- [x] 纯前端方案改造
- [x] 移除外部依赖
- [x] Memory Bank建设
- [ ] 性能优化
- [ ] 文档完善

## 相关文档

- [系统架构设计](system-architecture.md)
- [功能实现指南](implementation-guide.md)
- [运维操作手册](operations-manual.md)
