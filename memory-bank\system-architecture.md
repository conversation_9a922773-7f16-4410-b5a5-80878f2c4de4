# OTA订单处理系统 - 系统架构设计

## 架构概述

### 架构原则
1. **纯前端架构**: 所有逻辑在浏览器中执行，无需后端服务器
2. **模块化设计**: 功能模块独立，低耦合高内聚
3. **API优先**: 通过标准API与外部服务集成
4. **响应式设计**: 适配不同设备和屏幕尺寸
5. **渐进增强**: 基础功能优先，高级功能可选

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    浏览器环境 (Browser)                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   用户界面层     │  │   业务逻辑层     │  │   数据访问层     │ │
│  │ (Presentation)  │  │   (Business)    │  │    (Data)      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      外部服务层                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Google Vision   │  │  DeepSeek/      │  │  GoMyHire API   │ │
│  │     API         │  │  Gemini API     │  │      服务       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 分层架构设计

### 1. 用户界面层 (Presentation Layer)

#### 1.1 组件结构
```
UI Components
├── App (主应用容器)
│   ├── LoginModal (登录模态框)
│   ├── Header (页面头部)
│   │   ├── GeminiStatusIndicator (Gemini状态指示器)
│   │   └── UserInfo (用户信息)
│   ├── MainContent (主内容区)
│   │   ├── InputTabs (输入标签页)
│   │   │   ├── TextInput (文字输入)
│   │   │   └── ImageUpload (图片上传)
│   │   ├── ImageAnalysisResult (图像分析结果)
│   │   ├── OTASelector (OTA类型选择)
│   │   ├── ProcessButton (处理按钮)
│   │   ├── ResultPreview (结果预览)
│   │   └── ActionButtons (操作按钮)
│   └── StatusBar (状态栏)
└── Utils
    ├── LoadingSpinner (加载动画)
    ├── ErrorMessage (错误提示)
    └── SuccessMessage (成功提示)
```

#### 1.2 界面状态管理
```javascript
// 应用状态枚举
const APP_STATES = {
    LOADING: 'loading',           // 加载中
    LOGIN_REQUIRED: 'login',      // 需要登录
    READY: 'ready',              // 就绪状态
    PROCESSING: 'processing',     // 处理中
    IMAGE_ANALYSIS: 'analysis',   // 图像分析中
    PREVIEW: 'preview',          // 预览结果
    CREATING: 'creating',        // 创建订单中
    COMPLETED: 'completed'       // 完成
};

// 界面状态转换
State Transitions:
LOADING → LOGIN_REQUIRED → READY → PROCESSING → PREVIEW → CREATING → COMPLETED
                    ↑         ↑                    ↓
                    └─────────┴────────────────────┘
```

#### 1.3 响应式设计
- **桌面端**: 1200px+ 三栏布局
- **平板端**: 768px-1199px 两栏布局
- **手机端**: <768px 单栏布局
- **断点设置**: 使用CSS媒体查询实现

### 2. 业务逻辑层 (Business Logic Layer)

#### 2.1 核心服务模块

```javascript
// 服务架构
Services
├── AuthService (认证服务)
│   ├── login()
│   ├── logout()
│   ├── checkAuthStatus()
│   └── refreshToken()
├── APIService (API服务)
│   ├── getBackendUsers()
│   ├── getSubCategories()
│   ├── getCarTypes()
│   └── createOrder()
├── ImageService (图像服务)
│   ├── extractTextFromImage()
│   ├── analyzeImageContent()
│   ├── callGoogleVisionAPI()
│   └── validateImageFile()
├── LLMService (语言模型服务)
│   ├── processWithDeepSeek()
│   ├── processWithGemini()
│   ├── selectBestLLM()
│   └── processOrderWithAI()
├── SmartSelectionService (智能选择服务)
│   ├── selectBackendUser()
│   ├── selectSubCategory()
│   ├── selectCarType()
│   └── getSelectionSummary()
├── OrderProcessor (订单处理器)
│   ├── parseOrderData()
│   ├── validateOrderData()
│   ├── formatOrderData()
│   ├── generateReference()
│   └── buildOrderData()
├── Logger (日志服务)
│   ├── logInfo()
│   ├── logError()
│   ├── logApiResponse()
│   └── logSmartSelection()
└── StorageService (存储服务)
    ├── saveToLocal()
    ├── loadFromLocal()
    ├── clearStorage()
    └── exportData()
```

#### 2.2 数据流设计

```
用户输入 → 数据验证 → AI处理 → 智能选择 → 结果解析 → 用户确认 → API调用 → 状态更新
    ↓         ↓         ↓         ↓         ↓         ↓         ↓         ↓
  Input   Validate   Vision/   Smart     Parse    Confirm   Create   Update
   Data     Data      LLM     Selection  Result   Result   Order    Status
```

#### 2.3 AI服务架构

```
图像输入 → Google Vision API → 文字提取
                                    ↓
文字输入 ────────────────────────→ DeepSeek API (主要)
                                    ↓ (15秒超时)
                                Gemini API (备用)
                                    ↓
                              结构化订单数据
```

### 3. 数据访问层 (Data Access Layer)

#### 3.1 本地存储设计

```javascript
// LocalStorage 数据结构
LocalStorage Schema:
{
    // 认证信息
    "ota_system_token": "jwt_token_string",
    "ota_system_user": {
        "id": 1,
        "email": "<EMAIL>",
        "name": "User Name"
    },
    
    // 系统数据缓存
    "ota_backend_users": [...],
    "ota_sub_categories": [...],
    "ota_car_types": [...],
    
    // 会话数据
    "ota_last_login": "2024-12-19T10:00:00Z",
    "ota_current_session": {
        "input_data": "...",
        "processed_results": [...],
        "current_step": "preview"
    },
    
    // 配置信息
    "ota_config": {
        "gemini_api_key": "encrypted_key",
        "deepseek_api_key": "encrypted_key",
        "google_vision_api_key": "encrypted_key",
        "default_ota_type": "auto",
        "auto_save_enabled": true
    }
}
```

#### 3.2 数据模型定义

```javascript
// 订单数据模型
const OrderModel = {
    // 基础信息
    id: String,                    // 订单ID
    ota_reference_number: String,  // OTA参考号
    customer_name: String,         // 客户姓名
    customer_contact: String,      // 客户联系方式
    passenger_number: Number,      // 乘客数量
    
    // 服务信息
    service_type: String,          // 服务类型 (pickup/dropoff)
    sub_category_id: Number,       // 子分类ID
    car_type_id: Number,          // 车型ID
    incharge_by_backend_user_id: Number, // 负责用户ID
    
    // 时间地点
    date: String,                 // 日期 (YYYY-MM-DD)
    time: String,                 // 时间 (HH:MM)
    pickup: String,               // 上车地点
    destination: String,          // 目的地
    
    // 航班信息
    flight_info: String,          // 航班信息
    flight_number: String,        // 航班号
    flight_time: String,          // 航班时间
    
    // 处理信息
    processed_at: String,         // 处理时间
    processed_by: String,         // 处理人
    status: String,               // 状态
    notes: String                 // 备注
};

// 智能选择结果模型
const SmartSelectionModel = {
    backendUserId: Number,        // 选择的后台用户ID
    backendUserName: String,      // 用户名称
    backendUserRole: String,      // 用户角色
    subCategoryId: Number,        // 选择的子分类ID
    subCategoryName: String,      // 分类名称
    carTypeId: Number,           // 选择的车型ID
    carTypeName: String,         // 车型名称
    selectionReasons: {          // 选择原因
        user: String,
        category: String,
        carType: String
    }
};
```

## 外部服务集成

### 1. Google Vision API 集成

#### 1.1 API配置
```javascript
const GOOGLE_VISION_CONFIG = {
    API_URL: 'https://vision.googleapis.com/v1/images:annotate',
    API_KEY: 'YOUR_API_KEY',
    
    FEATURES: {
        TEXT_DETECTION: 'TEXT_DETECTION',
        DOCUMENT_TEXT_DETECTION: 'DOCUMENT_TEXT_DETECTION',
        LABEL_DETECTION: 'LABEL_DETECTION',
        OBJECT_LOCALIZATION: 'OBJECT_LOCALIZATION'
    },
    
    MAX_RESULTS: 10,
    TIMEOUT: 15000
};
```

#### 1.2 请求格式
```javascript
// 图像分析请求
const visionRequest = {
    requests: [{
        image: { content: base64ImageData },
        features: [
            { type: 'TEXT_DETECTION', maxResults: 10 },
            { type: 'DOCUMENT_TEXT_DETECTION', maxResults: 10 },
            { type: 'LABEL_DETECTION', maxResults: 10 },
            { type: 'OBJECT_LOCALIZATION', maxResults: 10 }
        ]
    }]
};
```

### 2. DeepSeek API 集成

#### 2.1 API配置
```javascript
const DEEPSEEK_CONFIG = {
    API_URL: 'https://api.deepseek.com/v1/chat/completions',
    API_KEY: 'YOUR_API_KEY',
    MODEL: 'deepseek-chat',
    
    MODEL_CONFIG: {
        temperature: 0.7,
        max_tokens: 2048,
        top_p: 0.95
    },
    
    TIMEOUT: 15000
};
```

### 3. Gemini API 集成

#### 3.1 API配置
```javascript
const GEMINI_CONFIG = {
    API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
    API_KEY: 'YOUR_API_KEY',
    
    MODEL_CONFIG: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 2048
    },
    
    TIMEOUT: 30000
};
```

### 4. GoMyHire API 集成

#### 4.1 API端点
```javascript
const GOMYHIRE_API = {
    BASE_URL: 'https://staging.gomyhire.com.my/api',
    
    ENDPOINTS: {
        LOGIN: '/login',
        BACKEND_USERS: '/backend_users',
        SUB_CATEGORIES: '/sub_category',
        CAR_TYPES: '/car_types',
        CREATE_ORDER: '/create_order'
    }
};
```

#### 4.2 认证机制
```javascript
// 登录流程
Authentication Flow:
1. 发送登录请求 (email + password)
2. 接收JWT token
3. 存储token到LocalStorage
4. 在后续请求中添加Authorization header
5. 处理token过期和刷新
```

## 智能选择算法

### 1. 后台用户选择逻辑
```javascript
优先级：
1. 指定用户 jcy1 (ID: 338) - 默认首选
2. Chong Dealer类型 → 寻找包含"chong"或"jcy"的用户
3. 回退到Sub_Admin角色用户
4. 最后选择Operator角色用户
```

### 2. 车型选择逻辑
```javascript
根据乘客人数智能选择：
- 1-2人 → Compact 5 Seater (ID: 5)
- 3-4人 → Comfort 5 Seater (ID: 6)
- 5-6人 → Standard Size MPV (ID: 16)
- 7-8人 → 10 Seater MPV/Van (ID: 20)
- 9-11人 → 12 Seater MPV (ID: 30)
- 12+人 → 更大型车辆
```

### 3. 子分类选择逻辑
```javascript
服务类型判断：
- 接机服务 → Pickup (ID: 7)
- 送机服务 → Dropoff (ID: 8)
- 携程订单 → 携程1 (ID: 15)
- 云顶包车 → KL to genting (ID: 9)
- 马六甲包车 → KL to melaka (ID: 10)
- 适耕庄包车 → Sekinchan (ID: 36)
- 通用包车 → Charter (ID: 43)
```

## 安全架构

### 1. 数据安全
```javascript
// 敏感数据处理策略
Sensitive Data Handling:
1. API密钥加密存储
2. 用户密码不在前端存储
3. 订单数据临时存储，定期清理
4. 个人信息脱敏处理
5. 传输过程HTTPS加密
```

### 2. API安全
```javascript
// API请求安全措施
API Security:
1. HTTPS强制传输
2. JWT token认证
3. 请求频率限制
4. 输入数据验证
5. 错误信息脱敏
```

## 性能优化

### 1. 前端性能优化
```javascript
// 资源加载优化
Resource Optimization:
1. CSS/JS文件压缩
2. 图片懒加载
3. 关键资源预加载
4. 非关键资源延迟加载
5. 浏览器缓存利用
```

### 2. 网络性能优化
```javascript
// 网络请求优化
Network Optimization:
1. 请求合并
2. 数据缓存
3. 并发请求控制
4. 超时处理
5. 重试机制
```

## 部署架构

### 1. 静态部署
```
Deployment Structure:
├── index.html (入口文件)
├── styles.css (样式文件)
├── app.js (主应用)
├── config.js (配置文件)
├── assets/ (静态资源)
│   ├── images/
│   ├── fonts/
│   └── icons/
├── docs/ (文档)
│   └── user-guide.md
└── memory-bank/ (项目文档)
    ├── project-overview.md
    ├── system-architecture.md
    ├── implementation-guide.md
    └── operations-manual.md
```

### 2. 部署选项
```javascript
// 部署方式
Deployment Options:
1. 本地文件系统 (file://)
2. 简单HTTP服务器 (python -m http.server)
3. 静态网站托管 (GitHub Pages, Netlify)
4. CDN分发 (CloudFlare, AWS CloudFront)
5. 企业内网部署
```
