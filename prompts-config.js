/**
 * @file prompts-config.js - Gemini 提示词配置文件
 * @description 包含所有OTA类型的Gemini AI提示词配置
 */

// 提示词配置对象
const GEMINI_PROMPTS = {
    


    // Chong Dealer 专用提示词
    CHONG_DEALER: {
        name: 'Chong Dealer订单处理',
        description: '专门处理Chong Dealer类型的订单',
        prompt: `你是一个专业的订单处理助手。请根据以下需求，对"【订单列表】"中的每条订单信息进行处理。

⚠️ **重要说明**：你只需要专注于订单内容的解析和格式化，不需要进行OTA类型识别。

------------------------------------------------
【需求说明】

1. **多重日期判断与修正：**  
   - 以当前日期（{current_date}）为锚点，对订单所填写的日期进行多次验算与推断，确保**最终输出的日期**为**最近且合理的未来日期**。  
   - 若发现订单日期已过或与当前日期相比不再合理，则应依次顺延，直至找到**最接近原始日期**但仍位于未来的日期。  
   - 若订单日期刚好是今天，或尚未到达，且**逻辑上可行**，则可使用原始日期。  
   - 在计算日期时，可按照以下优先级多次验证：  
     1. 如本月尚有同日并且未来可用，则使用本月该日；  
     2. 若本月该日已过或不合理，则顺延到下个月的同一天；  
     3. 若遇到跨年或当月无对应天数等特殊情况，则顺延至下一个最合理日期（例如 2 月无 30 号或 31 号时，需顺延到最近可行日期）。  

2. **时间计算：**  
   - **接机 (pickup)**：  
     - 使用航班"到达时间"作为 \`订单时间\`。  
   - **送机 (dropoff)**：  
     - 在航班"起飞时间"的基础上**减去 3.5 小时**，将结果作为 \`订单时间\`。  
   - 若原始时间无法直接解析，请在 \`other\` 字段中注明原始信息，并根据最近逻辑为 \`订单时间\` 赋合理数值。

3. **酒店名称：**  
   - 若酒店名是中文，请查询后替换为正确且标准的**英文名称**；  
   - 若已是英文，且与实际查询结果一致，则原样保留；  
   - 如有疑问，请参考以下网站：  
     - https://hotels.ctrip.com  
     - https://booking.com  
     - https://google.com  

4. **上下车地点逻辑：**  
   - **接机 (pickup)**：\`pickup = klia\`, \`drop = <英文酒店名>\`  
   - **送机 (dropoff)**：\`pickup = <英文酒店名>\`, \`drop = klia\`  
   - 任何备注或原始时间信息等，统一放入 \`other\` 字段中。

5. **最终结果：**  
   - 为避免歧义，请对**每条订单**都进行**多次日期逻辑检查**，若校验通过则输出修正后日期；若无法确定，请在 \`other\` 中说明原始信息和推断过程。
   - 输出必须为**纯文本**，不可添加多余解释或 markdown 语法。
   - 结果应为2025年
   - **只返回以下字段**（不要返回默认预设字段）：
     \`\`\`
     日期: YYYY-MM-DD
     时间: HH:MM
     姓名: [客人姓名]
     航班: [航班号]
     pickup: [上车地点]
     drop: [下车地点]
     other: [其他信息]
     \`\`\`

------------------------------------------------
【订单列表】
{input}

请严格按照以上要求处理订单信息，只返回非预设字段的内容。`
    },



    // 自动识别通用提示词
    AUTO_DETECT: {
        name: '通用订单处理',
        description: '通用的订单处理提示词，专注于订单内容解析',
        prompt: `你是一个专业的订单处理助手，请按照以下规则处理订单信息：

【处理规则】

1. **日期处理：**
   - 当前日期：{current_date}
   - 识别订单中的所有日期信息
   - 确保输出日期为未来日期
   - 如果日期已过，自动调整到下个月的同一天
   - 如果是今天或未来日期且合理，保持原日期

2. **时间处理：**
   - 接机：使用航班到达时间
   - 送机：航班起飞时间减去3.5小时
   - 如果时间信息不明确，根据常理推断

3. **联系信息：**
   - 提取客人姓名
   - 生成联系电话（基于日期+时间+航班+姓名）
   - 生成OTA参考号码：OTA-YYYYMMDD-XXX

4. **地点信息：**
   - 识别酒店名称，中文转英文
   - 确定上下车地点
   - 接机：机场到酒店
   - 送机：酒店到机场

5. **航班信息：**
   - 提取航班号
   - 识别航班时间
   - 确定是接机还是送机

【输出格式】
请以纯文本格式输出，每个字段一行：
\`\`\`
日期: YYYY-MM-DD
时间: HH:MM
姓名: [客人姓名]
电话: [联系电话]
航班: [航班号]
pickup: [上车地点]
drop: [下车地点]
other: [其他信息]
ota_reference_number: [OTA参考号]
\`\`\`

每条订单之间用空行分隔。

【订单内容】
{input}

请处理上述订单信息。`
    },

    // 其他OTA类型通用提示词
    OTHER_OTA: {
        name: '通用订单处理',
        description: '处理各种类型的订单，专注于内容解析',
        prompt: `你是一个通用的订单处理助手，专注于订单内容的解析和格式化：

【通用处理规则】

1. **内容解析：**
   - 识别订单类型（接机/送机）
   - 提取关键信息（日期、时间、姓名、航班等）
   - 识别特殊要求和备注

2. **标准化处理：**
   - 日期格式：YYYY-MM-DD
   - 时间格式：HH:MM（24小时制）
   - 确保所有日期为未来日期

3. **信息补全：**
   - 生成缺失的联系信息
   - 标准化地点名称
   - 生成唯一的参考号码

4. **质量控制：**
   - 验证信息的逻辑性
   - 标记可能的错误或疑问
   - 提供处理建议

【输出格式】
\`\`\`
日期: YYYY-MM-DD
时间: HH:MM
姓名: [客人姓名]
电话: [联系电话]
航班: [航班号]
pickup: [上车地点]
drop: [下车地点]
service_type: [服务类型：接机/送机]
other: [其他信息和备注]
ota_reference_number: [OTA参考号]
processing_notes: [处理说明]
\`\`\`

【订单内容】
{input}

请处理上述订单信息。`
    },

    // 图片OCR识别提示词
    IMAGE_OCR: {
        name: '图片OCR识别',
        description: '用于从图片中提取订单信息',
        prompt: `你是一个专业的图片文字识别和订单信息提取专家。请分析这张图片中的订单信息，并按照以下要求提取和处理：

【图片分析要求】

1. **文字识别：**
   - 识别图片中的所有文字内容
   - 注意中英文混合的情况
   - 识别表格、表单结构
   - 处理手写文字（如果有）

2. **信息提取：**
   - 日期和时间信息
   - 客人姓名和联系方式
   - 航班号和航班时间
   - 酒店名称和地址
   - 服务类型（接机/送机）
   - 特殊要求和备注

3. **数据验证：**
   - 检查日期格式的合理性
   - 验证航班号格式
   - 确认时间的逻辑性
   - 标记可能的OCR错误

【处理规则】

1. **日期处理：**
   - 当前日期：{current_date}
   - 确保识别的日期为未来日期
   - 如果日期已过，建议调整方案

2. **格式标准化：**
   - 日期：YYYY-MM-DD
   - 时间：HH:MM
   - 电话：标准格式

3. **信息补全：**
   - 生成缺失的OTA参考号
   - 标准化地点名称
   - 补充必要的联系信息

【输出格式】
请以以下格式输出识别结果：

\`\`\`
=== OCR识别原始内容 ===
[图片中识别到的原始文字内容]

=== 提取的订单信息 ===
日期: YYYY-MM-DD
时间: HH:MM
姓名: [客人姓名]
电话: [联系电话]
航班: [航班号]
pickup: [上车地点]
drop: [下车地点]
service_type: [接机/送机]
other: [其他信息]
ota_reference_number: [OTA参考号]

=== 处理说明 ===
- OCR置信度: [高/中/低]
- 可能的错误: [列出可能的识别错误]
- 建议检查: [需要人工确认的项目]
- 处理备注: [其他处理说明]
\`\`\`

请分析图片并提取订单信息。`
    },

    // 图片质量评估提示词
    IMAGE_QUALITY: {
        name: '图片质量评估',
        description: '评估图片质量和OCR可行性',
        prompt: `请评估这张图片的质量，并提供OCR识别的可行性分析：

【评估维度】

1. **图片清晰度：**
   - 分辨率是否足够
   - 文字是否清晰可读
   - 是否有模糊或失焦

2. **光线条件：**
   - 亮度是否适中
   - 是否有阴影遮挡
   - 对比度是否足够

3. **文字布局：**
   - 文字排列是否整齐
   - 是否有倾斜或变形
   - 表格结构是否完整

4. **干扰因素：**
   - 是否有背景干扰
   - 是否有污渍或折痕
   - 是否有反光或阴影

【输出格式】
\`\`\`
=== 图片质量评估 ===
整体质量: [优秀/良好/一般/较差]
清晰度: [评分1-10]
可读性: [评分1-10]
OCR成功率预估: [百分比]

=== 具体问题 ===
- [列出发现的问题]
- [提供改善建议]

=== 处理建议 ===
- [是否建议重新拍摄]
- [是否需要图片预处理]
- [OCR识别注意事项]
\`\`\`

请评估图片质量。`
    }
};

// 提示词工具类
class PromptManager {
    constructor() {
        this.prompts = GEMINI_PROMPTS;
    }

    /**
     * 获取指定类型的提示词
     * @param {string} type - 提示词类型
     * @param {Object} params - 参数对象
     * @returns {string} 处理后的提示词
     */
    getPrompt(type, params = {}) {
        const promptConfig = this.prompts[type];
        if (!promptConfig) {
            throw new Error(`未找到提示词类型: ${type}`);
        }

        let prompt = promptConfig.prompt;
        
        // 替换参数
        Object.keys(params).forEach(key => {
            const placeholder = `{${key}}`;
            prompt = prompt.replace(new RegExp(placeholder, 'g'), params[key]);
        });

        return prompt;
    }

    /**
     * 根据OTA类型获取对应的处理提示词
     * @param {string} otaType - OTA类型
     * @param {string} input - 输入内容
     * @param {string} currentDate - 当前日期
     * @returns {string} 处理后的提示词
     */
    getOTAPrompt(otaType, input, currentDate) {
        const params = {
            input: input,
            current_date: currentDate
        };

        switch (otaType.toLowerCase()) {
            case 'chong-dealer':
            case 'chong_dealer':
            case 'chong':
                return this.getPrompt('CHONG_DEALER', params);
            

            
            case 'auto':
            case 'auto-detect':
            case 'auto_detect':
                return this.getPrompt('AUTO_DETECT', params);
            
            case 'other':
            default:
                return this.getPrompt('OTHER_OTA', params);
        }
    }



    /**
     * 获取图片OCR提示词
     * @param {string} currentDate - 当前日期
     * @returns {string} OCR提示词
     */
    getImageOCRPrompt(currentDate) {
        return this.getPrompt('IMAGE_OCR', { current_date: currentDate });
    }

    /**
     * 获取图片质量评估提示词
     * @returns {string} 质量评估提示词
     */
    getImageQualityPrompt() {
        return this.getPrompt('IMAGE_QUALITY');
    }

    /**
     * 获取所有可用的提示词类型
     * @returns {Array} 提示词类型列表
     */
    getAvailableTypes() {
        return Object.keys(this.prompts).map(key => ({
            type: key,
            name: this.prompts[key].name,
            description: this.prompts[key].description
        }));
    }

    /**
     * 验证提示词参数
     * @param {string} type - 提示词类型
     * @param {Object} params - 参数对象
     * @returns {boolean} 验证结果
     */
    validateParams(type, params) {
        const promptConfig = this.prompts[type];
        if (!promptConfig) {
            return false;
        }

        // 检查必需的参数
        const requiredParams = this.extractRequiredParams(promptConfig.prompt);
        return requiredParams.every(param => params.hasOwnProperty(param));
    }

    /**
     * 从提示词中提取必需的参数
     * @param {string} prompt - 提示词内容
     * @returns {Array} 必需参数列表
     */
    extractRequiredParams(prompt) {
        const matches = prompt.match(/{([^}]+)}/g);
        if (!matches) return [];
        
        return matches.map(match => match.slice(1, -1));
    }
}

// 导出配置和工具类
if (typeof module !== 'undefined' && module.exports) {
    // Node.js 环境
    module.exports = {
        GEMINI_PROMPTS,
        PromptManager
    };
} else {
    // 浏览器环境
    window.GEMINI_PROMPTS = GEMINI_PROMPTS;
    window.PromptManager = PromptManager;
}

// 创建全局实例
const promptManager = new PromptManager();

// 使用示例
/*
// 1. 获取OTA类型识别提示词
const detectionPrompt = promptManager.getDetectionPrompt(orderContent);

// 2. 根据OTA类型获取处理提示词
const processingPrompt = promptManager.getOTAPrompt('chong-dealer', orderContent, '2024-12-20');

// 3. 获取图片OCR提示词
const ocrPrompt = promptManager.getImageOCRPrompt('2024-12-20');

// 4. 获取所有可用类型
const availableTypes = promptManager.getAvailableTypes();
console.log('可用的提示词类型:', availableTypes);
*/