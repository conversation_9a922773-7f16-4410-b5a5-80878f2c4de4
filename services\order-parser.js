/**
 * @file order-parser.js - 订单解析服务
 * @description 整合本地关键词检测和LLM处理的订单解析服务
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 */

/**
 * @class OrderParser - 订单解析服务类
 * @description 提供统一的订单解析接口，支持本地检测和LLM处理
 */
class OrderParser {
    /**
     * @function constructor - 构造函数
     */
    constructor() {
        this.llmService = new LLMService();
        this.otaDetectionRules = SYSTEM_CONFIG.OTA_TYPES;
    }

    /**
     * @function parseOrders - 解析订单文本
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型（可选，默认自动检测）
     * @returns {Promise<object>} 解析结果
     */
    async parseOrders(text, otaType = 'auto') {
        const startTime = Date.now();
        
        logger.info('订单解析', '开始解析订单', {
            textLength: text.length,
            specifiedOtaType: otaType
        });

        try {
            // 1. OTA类型检测（如果未指定）
            let detectedOtaType = otaType;
            if (otaType === 'auto') {
                detectedOtaType = this.detectOtaType(text);
                logger.info('订单解析', 'OTA类型检测完成', {
                    detectedType: detectedOtaType,
                    confidence: this.getDetectionConfidence(text, detectedOtaType)
                });
            }

            // 2. 根据OTA类型选择解析策略
            let parseResult;
            if (detectedOtaType === 'chong-dealer') {
                // Chong Dealer使用本地关键词检测 + LLM增强
                parseResult = await this.parseChongDealerOrders(text);
            } else {
                // 其他类型使用LLM处理
                parseResult = await this.parseLLMOrders(text, detectedOtaType);
            }

            const processingTime = Date.now() - startTime;

            logger.success('订单解析', '订单解析完成', {
                otaType: detectedOtaType,
                orderCount: parseResult.orders?.length || 0,
                processingTime: `${processingTime}ms`,
                success: parseResult.success
            });

            return {
                success: parseResult.success,
                orders: parseResult.orders || [],
                otaType: detectedOtaType,
                processingTime: processingTime,
                metadata: {
                    originalText: text,
                    detectionMethod: detectedOtaType === 'chong-dealer' ? 'local+llm' : 'llm',
                    ...parseResult.metadata
                }
            };

        } catch (error) {
            const processingTime = Date.now() - startTime;
            
            logger.error('订单解析', '订单解析失败', {
                error: error.message,
                processingTime: `${processingTime}ms`
            });

            return {
                success: false,
                error: error.message,
                orders: [],
                processingTime: processingTime
            };
        }
    }

    /**
     * @function detectOtaType - 检测OTA类型
     * @param {string} text - 订单文本
     * @returns {string} 检测到的OTA类型
     */
    detectOtaType(text) {
        const textLower = text.toLowerCase();
        
        // 检查Chong Dealer关键词
        const chongKeywords = this.otaDetectionRules['chong-dealer'].keywordPatterns;
        let chongMatches = 0;
        
        for (const pattern of chongKeywords) {
            const regex = new RegExp(pattern, 'i');
            if (regex.test(text)) {
                chongMatches++;
            }
        }

        // 如果匹配到足够的Chong Dealer关键词
        if (chongMatches >= this.otaDetectionRules['chong-dealer'].minimumMatches) {
            return 'chong-dealer';
        }

        // 检查其他OTA类型的关键词
        for (const [otaType, config] of Object.entries(this.otaDetectionRules)) {
            if (otaType === 'chong-dealer' || !config.keywordPatterns) continue;
            
            let matches = 0;
            for (const pattern of config.keywordPatterns) {
                const regex = new RegExp(pattern, 'i');
                if (regex.test(text)) {
                    matches++;
                }
            }
            
            if (matches >= (config.minimumMatches || 1)) {
                return otaType;
            }
        }

        // 默认返回其他类型
        return 'other';
    }

    /**
     * @function getDetectionConfidence - 获取检测置信度
     * @param {string} text - 订单文本
     * @param {string} otaType - 检测到的OTA类型
     * @returns {number} 置信度（0-1）
     */
    getDetectionConfidence(text, otaType) {
        const config = this.otaDetectionRules[otaType];
        if (!config || !config.keywordPatterns) return 0.5;

        let matches = 0;
        for (const pattern of config.keywordPatterns) {
            const regex = new RegExp(pattern, 'i');
            if (regex.test(text)) {
                matches++;
            }
        }

        return Math.min(matches / config.keywordPatterns.length, 1.0);
    }

    /**
     * @function parseChongDealerOrders - 解析Chong Dealer订单
     * @param {string} text - 订单文本
     * @returns {Promise<object>} 解析结果
     */
    async parseChongDealerOrders(text) {
        logger.info('订单解析', '使用Chong Dealer专用解析器');

        try {
            // 1. 本地关键词提取基础信息
            const localResult = this.extractChongDealerInfo(text);
            
            // 2. 使用LLM增强和验证
            const llmResult = await this.llmService.processOrderText(text, 'chong-dealer');
            
            if (llmResult.success) {
                // 合并本地提取和LLM结果
                return {
                    success: true,
                    orders: this.mergeLocalAndLLMResults(localResult, llmResult.data),
                    metadata: {
                        localExtraction: localResult,
                        llmProvider: llmResult.provider,
                        processingTime: llmResult.processingTime
                    }
                };
            } else {
                // LLM失败，使用本地提取结果
                logger.warn('订单解析', 'LLM处理失败，使用本地提取结果', {
                    llmError: llmResult.error
                });
                
                return {
                    success: true,
                    orders: localResult.orders || [],
                    metadata: {
                        localExtraction: localResult,
                        llmError: llmResult.error,
                        fallbackUsed: true
                    }
                };
            }

        } catch (error) {
            logger.error('订单解析', 'Chong Dealer解析失败', { error: error.message });
            return {
                success: false,
                error: error.message,
                orders: []
            };
        }
    }

    /**
     * @function parseLLMOrders - 使用LLM解析订单
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型
     * @returns {Promise<object>} 解析结果
     */
    async parseLLMOrders(text, otaType) {
        logger.info('订单解析', '使用LLM解析订单', { otaType });

        try {
            const llmResult = await this.llmService.processOrderText(text, otaType);
            
            if (llmResult.success) {
                return {
                    success: true,
                    orders: llmResult.data.orders || [],
                    metadata: {
                        llmProvider: llmResult.provider,
                        processingTime: llmResult.processingTime,
                        rawResponse: llmResult.data.rawContent
                    }
                };
            } else {
                return {
                    success: false,
                    error: llmResult.error,
                    orders: []
                };
            }

        } catch (error) {
            logger.error('订单解析', 'LLM解析失败', { error: error.message });
            return {
                success: false,
                error: error.message,
                orders: []
            };
        }
    }

    /**
     * @function extractChongDealerInfo - 提取Chong Dealer基础信息
     * @param {string} text - 订单文本
     * @returns {object} 提取结果
     */
    extractChongDealerInfo(text) {
        const orders = [];
        const lines = text.split('\n');
        
        // 基础关键词提取逻辑
        const patterns = {
            customerName: /(客人姓名|姓名)[:：]\s*([^\n]+)/i,
            serviceDate: /(\d{1,2}[月\.]\d{1,2}日?)/,
            serviceTime: /(\d{1,2}[点时:]?\d{0,2})/,
            flightNumber: /([A-Z]{1,3}\d{2,4})/,
            serviceType: /(接机|送机)/,
            passengerCount: /(\d+)(人|大人|位)/,
            carType: /(经济|舒适|豪华|阿尔法|Velfire|Alphard)/i,
            hotel: /(酒店|住宿)[:：]\s*([^\n]+)/,
            specialServices: /(举牌|机场转乘|包车)/
        };

        // 简单的信息提取
        const extractedInfo = {};
        for (const [key, pattern] of Object.entries(patterns)) {
            const match = text.match(pattern);
            if (match) {
                extractedInfo[key] = match[1] || match[2] || match[0];
            }
        }

        // 构建基础订单对象
        if (extractedInfo.customerName || extractedInfo.flightNumber) {
            orders.push({
                customerName: extractedInfo.customerName || '',
                serviceDate: extractedInfo.serviceDate || '',
                serviceTime: extractedInfo.serviceTime || '',
                flightNumber: extractedInfo.flightNumber || '',
                serviceType: extractedInfo.serviceType || '',
                passengerCount: extractedInfo.passengerCount ? parseInt(extractedInfo.passengerCount) : 1,
                carType: extractedInfo.carType || '',
                hotel: extractedInfo.hotel || '',
                specialServices: extractedInfo.specialServices ? [extractedInfo.specialServices] : [],
                originalText: text,
                extractionMethod: 'local_keywords'
            });
        }

        return {
            orders: orders,
            extractedFields: Object.keys(extractedInfo),
            confidence: orders.length > 0 ? 0.7 : 0.3
        };
    }

    /**
     * @function mergeLocalAndLLMResults - 合并本地提取和LLM结果
     * @param {object} localResult - 本地提取结果
     * @param {object} llmResult - LLM处理结果
     * @returns {array} 合并后的订单列表
     */
    mergeLocalAndLLMResults(localResult, llmResult) {
        // 优先使用LLM结果，本地结果作为补充和验证
        const mergedOrders = [];
        
        if (llmResult.orders && llmResult.orders.length > 0) {
            // 使用LLM结果为主
            for (const llmOrder of llmResult.orders) {
                const mergedOrder = {
                    ...llmOrder,
                    processingMethod: 'llm_enhanced',
                    localValidation: localResult.confidence > 0.5
                };
                mergedOrders.push(mergedOrder);
            }
        } else if (localResult.orders && localResult.orders.length > 0) {
            // 回退到本地结果
            for (const localOrder of localResult.orders) {
                const mergedOrder = {
                    ...localOrder,
                    processingMethod: 'local_fallback',
                    confidence: localResult.confidence
                };
                mergedOrders.push(mergedOrder);
            }
        }

        return mergedOrders;
    }

    /**
     * @function validateOrder - 验证订单信息
     * @param {object} order - 订单对象
     * @returns {object} 验证结果
     */
    validateOrder(order) {
        const errors = [];
        const warnings = [];

        // 必填字段检查
        if (!order.customerName && !order.flightNumber) {
            errors.push('缺少客人姓名或航班号');
        }

        if (!order.serviceType) {
            warnings.push('未指定服务类型');
        }

        if (!order.serviceDate) {
            warnings.push('未指定服务日期');
        }

        return {
            isValid: errors.length === 0,
            errors: errors,
            warnings: warnings
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OrderParser;
} else if (typeof window !== 'undefined') {
    window.OrderParser = OrderParser;
}
