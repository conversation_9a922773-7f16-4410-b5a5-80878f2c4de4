/**
 * @file ChongDealerOrderParser.js - Chong Dealer订单解析器
 * @description 专门解析来自Chong Dealer OTA平台的订单格式
 */

class ChongDealerOrderParser {
    constructor() {
        // 订单分隔符模式
        this.orderSeparators = [
            /^-{3,}$/m,                           // 横线分隔 -------
            /^\d+\./m,                            // 数字编号 1. 2.
            /^\d{1,2}[月\.]\d{1,2}日?[:：]/m,        // 日期标题 3月2日：
            /^\*[^*]+\*$/m,                       // 处理人标记 *京鱼*
            /^_{10,}$/m                           // 下划线分隔
        ];
        
        // 字段提取正则模式
        this.fieldPatterns = {
            // 客人信息
            customerName: /(姓名|客人姓名|客人)[:：]\s*([^0-9\n]+?)(?:\s*\d+人)?/i,
            passengerCount: /(\d+)(人|大人|位)/,
            
            // 时间信息
            serviceDate: /(\d{1,2}[月\.]\d{1,2}日?)/,
            serviceTime: /(\d{1,2}[点时:]?\d{0,2})/,
            flightTime: /(\d{2}:\d{2})/,
            
            // 航班信息
            flightNumber: /([A-Z]{1,3}\d{2,4})/,
            flightRoute: /([^-\n]+)\s*-\s*([^0-9\n]+)/,
            
            // 服务类型
            serviceType: /(接机|送机)/,
            arrivalFlight: /(抵达|到达)/,
            departureFlight: /(起飞|出发)/,
            
            // 车型信息
            carType: /(经济|舒适|豪华|阿尔法|Velfire|Alphard)/i,
            seatCount: /(\d+)座/,
            
            // 地址信息
            hotel: /(酒店|住宿)[:：]\s*([^\n]+)/,
            pickupAddress: /(接客地址|出发)[:：]\s*([^\n]+)/,
            dropoffAddress: /(送客地址|抵达)[:：]\s*([^\n]+)/,
            
            // 联系方式
            phone: /(联系方式|电话)[:：]\s*([^\n]+)/,
            wechat: /(微信号?)[:：]\s*([^\n]+)/,
            
            // 价格信息
            pricing: /(结算|价格)[:：]\s*([^\n]+)/,
            
            // 特殊服务
            signService: /举牌/,
            transferService: /(机场转乘|转站楼)/,
            charterService: /包车.*?(\d+)小时/
        };
        
        // OTA类型识别
        this.otaPatterns = {
            chong: /OTA[:：]\s*CHONG/i,
            tee: /OTA[:：]\s*Tee/i,
            shan: /OTA[:：]\s*Shan/i
        };
        
        // 订单状态标识
        this.statusPatterns = {
            cancelled: /\*取消\*/,
            confirmed: /\*Confirm\*/i,
            edited: /这条消息已经过编辑/
        };
    }

    /**
     * @function parseOrders - 解析订单文本
     * @param {string} content - 聊天记录内容
     * @returns {Array} 解析后的订单数组
     */
    parseOrders(content) {
        try {
            // 1. 预处理文本
            const cleanContent = this.preprocessContent(content);
            
            // 2. 按分隔符分段
            const segments = this.splitIntoSegments(cleanContent);
            
            // 3. 解析每个段落
            const orders = [];
            for (const segment of segments) {
                const parsedOrders = this.parseSegment(segment);
                orders.push(...parsedOrders);
            }
            
            // 4. 后处理和验证
            return this.postProcessOrders(orders);
            
        } catch (error) {
            console.error('订单解析失败:', error);
            return [];
        }
    }

    /**
     * @function preprocessContent - 预处理文本内容
     * @param {string} content - 原始内容
     * @returns {string} 清理后的内容
     */
    preprocessContent(content) {
        return content
            .replace(/‎/g, '')                    // 移除不可见字符
            .replace(/\r\n/g, '\n')              // 统一换行符
            .replace(/^\[.*?\]\s*/gm, '')        // 移除时间戳
            .replace(/^.*?[:：]\s*‎.*$/gm, '')     // 移除系统消息
            .replace(/‎<.*?>$/gm, '')             // 移除编辑标记
            .replace(/^\s*$/gm, '')              // 移除空行
            .trim();
    }

    /**
     * @function splitIntoSegments - 将内容分割为订单段落
     * @param {string} content - 清理后的内容
     * @returns {Array} 订单段落数组
     */
    splitIntoSegments(content) {
        const segments = [];
        const lines = content.split('\n');
        let currentSegment = [];
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // 检查是否为分隔符
            const isSeparator = this.orderSeparators.some(pattern => pattern.test(line));
            
            if (isSeparator && currentSegment.length > 0) {
                // 遇到分隔符，保存当前段落
                segments.push(currentSegment.join('\n'));
                currentSegment = [];
            } else if (line) {
                currentSegment.push(line);
            }
        }
        
        // 添加最后一个段落
        if (currentSegment.length > 0) {
            segments.push(currentSegment.join('\n'));
        }
        
        return segments.filter(segment => segment.trim().length > 10); // 过滤太短的段落
    }

    /**
     * @function parseSegment - 解析单个订单段落
     * @param {string} segment - 订单段落文本
     * @returns {Array} 解析后的订单对象数组
     */
    parseSegment(segment) {
        const orders = [];
        
        // 检查是否包含多个日期（多程订单）
        const dateMatches = segment.match(/\d{1,2}[月\.]\d{1,2}日?/g);
        
        if (dateMatches && dateMatches.length > 1) {
            // 多程订单处理
            orders.push(...this.parseMultiTripOrder(segment));
        } else {
            // 单程订单处理
            const order = this.parseSingleOrder(segment);
            if (order && this.isValidOrder(order)) {
                orders.push(order);
            }
        }
        
        return orders;
    }

    /**
     * @function parseSingleOrder - 解析单程订单
     * @param {string} text - 订单文本
     * @returns {Object} 订单对象
     */
    parseSingleOrder(text) {
        const order = {
            originalText: text,
            customerName: '',
            passengerCount: 1,
            serviceType: '',
            serviceDate: '',
            serviceTime: '',
            flightNumber: '',
            flightTime: '',
            carType: '',
            seatCount: 5,
            hotel: '',
            pickupAddress: '',
            dropoffAddress: '',
            phone: '',
            wechat: '',
            pricing: '',
            specialServices: [],
            otaType: 'chong_dealer',
            status: 'active'
        };

        // 提取各字段信息
        this.extractCustomerInfo(text, order);
        this.extractServiceInfo(text, order);
        this.extractFlightInfo(text, order);
        this.extractCarInfo(text, order);
        this.extractAddressInfo(text, order);
        this.extractContactInfo(text, order);
        this.extractPricingInfo(text, order);
        this.extractSpecialServices(text, order);
        this.extractOtaType(text, order);
        this.extractStatus(text, order);

        return order;
    }

    /**
     * @function extractCustomerInfo - 提取客人信息
     * @param {string} text - 订单文本
     * @param {Object} order - 订单对象
     */
    extractCustomerInfo(text, order) {
        // 提取客人姓名
        const nameMatch = text.match(this.fieldPatterns.customerName);
        if (nameMatch) {
            order.customerName = nameMatch[2].trim();
        }

        // 提取人数
        const countMatch = text.match(this.fieldPatterns.passengerCount);
        if (countMatch) {
            order.passengerCount = parseInt(countMatch[1]);
        }
    }

    /**
     * @function extractServiceInfo - 提取服务信息
     * @param {string} text - 订单文本
     * @param {Object} order - 订单对象
     */
    extractServiceInfo(text, order) {
        // 提取服务类型
        const serviceMatch = text.match(this.fieldPatterns.serviceType);
        if (serviceMatch) {
            order.serviceType = serviceMatch[1];
        } else {
            // 通过航班信息推断服务类型
            if (this.fieldPatterns.arrivalFlight.test(text)) {
                order.serviceType = '接机';
            } else if (this.fieldPatterns.departureFlight.test(text)) {
                order.serviceType = '送机';
            }
        }

        // 提取服务日期
        const dateMatch = text.match(this.fieldPatterns.serviceDate);
        if (dateMatch) {
            order.serviceDate = this.normalizeDate(dateMatch[1]);
        }

        // 提取服务时间
        const timeMatch = text.match(this.fieldPatterns.serviceTime);
        if (timeMatch) {
            order.serviceTime = timeMatch[1];
        }
    }

    /**
     * @function extractFlightInfo - 提取航班信息
     * @param {string} text - 订单文本
     * @param {Object} order - 订单对象
     */
    extractFlightInfo(text, order) {
        // 提取航班号
        const flightMatch = text.match(this.fieldPatterns.flightNumber);
        if (flightMatch) {
            order.flightNumber = flightMatch[1];
        }

        // 提取航班时间
        const flightTimeMatch = text.match(this.fieldPatterns.flightTime);
        if (flightTimeMatch) {
            order.flightTime = flightTimeMatch[1];
        }
    }

    /**
     * @function extractCarInfo - 提取车型信息
     * @param {string} text - 订单文本
     * @param {Object} order - 订单对象
     */
    extractCarInfo(text, order) {
        // 提取车型
        const carTypeMatch = text.match(this.fieldPatterns.carType);
        if (carTypeMatch) {
            order.carType = carTypeMatch[1];
        }

        // 提取座位数
        const seatMatch = text.match(this.fieldPatterns.seatCount);
        if (seatMatch) {
            order.seatCount = parseInt(seatMatch[1]);
        }
    }

    /**
     * @function extractAddressInfo - 提取地址信息
     * @param {string} text - 订单文本
     * @param {Object} order - 订单对象
     */
    extractAddressInfo(text, order) {
        // 提取酒店
        const hotelMatch = text.match(this.fieldPatterns.hotel);
        if (hotelMatch) {
            order.hotel = hotelMatch[2].trim();
        }

        // 提取接客地址
        const pickupMatch = text.match(this.fieldPatterns.pickupAddress);
        if (pickupMatch) {
            order.pickupAddress = pickupMatch[2].trim();
        }

        // 提取送客地址
        const dropoffMatch = text.match(this.fieldPatterns.dropoffAddress);
        if (dropoffMatch) {
            order.dropoffAddress = dropoffMatch[2].trim();
        }
    }

    /**
     * @function extractContactInfo - 提取联系信息
     * @param {string} text - 订单文本
     * @param {Object} order - 订单对象
     */
    extractContactInfo(text, order) {
        // 提取电话
        const phoneMatch = text.match(this.fieldPatterns.phone);
        if (phoneMatch) {
            order.phone = phoneMatch[2].trim();
        }

        // 提取微信
        const wechatMatch = text.match(this.fieldPatterns.wechat);
        if (wechatMatch) {
            order.wechat = wechatMatch[2].trim();
        }
    }

    /**
     * @function extractPricingInfo - 提取价格信息
     * @param {string} text - 订单文本
     * @param {Object} order - 订单对象
     */
    extractPricingInfo(text, order) {
        const pricingMatch = text.match(this.fieldPatterns.pricing);
        if (pricingMatch) {
            order.pricing = pricingMatch[2].trim();
        }
    }

    /**
     * @function extractSpecialServices - 提取特殊服务
     * @param {string} text - 订单文本
     * @param {Object} order - 订单对象
     */
    extractSpecialServices(text, order) {
        if (this.fieldPatterns.signService.test(text)) {
            order.specialServices.push('举牌');
        }

        if (this.fieldPatterns.transferService.test(text)) {
            order.specialServices.push('机场转乘');
        }

        const charterMatch = text.match(this.fieldPatterns.charterService);
        if (charterMatch) {
            order.specialServices.push(`包车${charterMatch[1]}小时`);
        }
    }

    /**
     * @function extractOtaType - 提取OTA类型
     * @param {string} text - 订单文本
     * @param {Object} order - 订单对象
     */
    extractOtaType(text, order) {
        for (const [type, pattern] of Object.entries(this.otaPatterns)) {
            if (pattern.test(text)) {
                order.otaType = type;
                break;
            }
        }
    }

    /**
     * @function extractStatus - 提取订单状态
     * @param {string} text - 订单文本
     * @param {Object} order - 订单对象
     */
    extractStatus(text, order) {
        if (this.statusPatterns.cancelled.test(text)) {
            order.status = 'cancelled';
        } else if (this.statusPatterns.confirmed.test(text)) {
            order.status = 'confirmed';
        } else if (this.statusPatterns.edited.test(text)) {
            order.status = 'edited';
        }
    }

    /**
     * @function parseMultiTripOrder - 解析多程订单
     * @param {string} text - 订单文本
     * @returns {Array} 订单对象数组
     */
    parseMultiTripOrder(text) {
        const orders = [];
        const lines = text.split('\n');
        let currentTrip = [];
        let commonInfo = '';

        // 分离公共信息和行程信息
        for (const line of lines) {
            if (/\d{1,2}[月\.]\d{1,2}日?[:：]/.test(line) || /\d{1,2}\.\d{1,2}(接机|送机)/.test(line)) {
                if (currentTrip.length > 0) {
                    // 处理前一个行程
                    const tripText = currentTrip.join('\n') + '\n' + commonInfo;
                    const order = this.parseSingleOrder(tripText);
                    if (order && this.isValidOrder(order)) {
                        orders.push(order);
                    }
                    currentTrip = [];
                }
                currentTrip.push(line);
            } else if (currentTrip.length > 0) {
                currentTrip.push(line);
            } else {
                commonInfo += line + '\n';
            }
        }

        // 处理最后一个行程
        if (currentTrip.length > 0) {
            const tripText = currentTrip.join('\n') + '\n' + commonInfo;
            const order = this.parseSingleOrder(tripText);
            if (order && this.isValidOrder(order)) {
                orders.push(order);
            }
        }

        return orders;
    }

    /**
     * @function normalizeDate - 标准化日期格式
     * @param {string} dateStr - 日期字符串
     * @returns {string} 标准化后的日期
     */
    normalizeDate(dateStr) {
        // 将各种日期格式统一为 MM-DD 格式
        const match = dateStr.match(/(\d{1,2})[月\.](\d{1,2})/);
        if (match) {
            const month = match[1].padStart(2, '0');
            const day = match[2].padStart(2, '0');
            return `${month}-${day}`;
        }
        return dateStr;
    }

    /**
     * @function isValidOrder - 验证订单是否有效
     * @param {Object} order - 订单对象
     * @returns {boolean} 是否有效
     */
    isValidOrder(order) {
        // 基本验证：必须有客人姓名或航班号
        return order && (
            order.customerName.length > 0 ||
            order.flightNumber.length > 0 ||
            order.serviceType.length > 0
        );
    }

    /**
     * @function postProcessOrders - 后处理订单数组
     * @param {Array} orders - 原始订单数组
     * @returns {Array} 处理后的订单数组
     */
    postProcessOrders(orders) {
        // 1. 过滤无效订单
        const validOrders = orders.filter(order => this.isValidOrder(order));

        // 2. 关联同一客人的多程订单
        const groupedOrders = this.groupOrdersByCustomer(validOrders);

        // 3. 补全缺失信息
        const completedOrders = groupedOrders.map(order => this.completeOrderInfo(order));

        // 4. 添加唯一标识
        return completedOrders.map((order, index) => ({
            ...order,
            id: `chong_${Date.now()}_${index}`,
            parsedAt: new Date().toISOString()
        }));
    }

    /**
     * @function groupOrdersByCustomer - 按客人分组订单
     * @param {Array} orders - 订单数组
     * @returns {Array} 分组后的订单数组
     */
    groupOrdersByCustomer(orders) {
        const customerGroups = new Map();

        for (const order of orders) {
            const key = order.customerName || order.phone || 'unknown';
            if (!customerGroups.has(key)) {
                customerGroups.set(key, []);
            }
            customerGroups.get(key).push(order);
        }

        // 为同一客人的订单添加关联信息
        const result = [];
        for (const [customer, customerOrders] of customerGroups) {
            if (customerOrders.length > 1) {
                customerOrders.forEach((order, index) => {
                    order.isMultiTrip = true;
                    order.tripIndex = index + 1;
                    order.totalTrips = customerOrders.length;
                });
            }
            result.push(...customerOrders);
        }

        return result;
    }

    /**
     * @function completeOrderInfo - 补全订单信息
     * @param {Object} order - 订单对象
     * @returns {Object} 补全后的订单对象
     */
    completeOrderInfo(order) {
        // 根据服务类型补全地址信息
        if (order.serviceType === '接机' && !order.pickupAddress && order.flightNumber) {
            order.pickupAddress = '吉隆坡国际机场';
            order.dropoffAddress = order.hotel || '市区酒店';
        } else if (order.serviceType === '送机' && !order.dropoffAddress && order.flightNumber) {
            order.pickupAddress = order.hotel || '市区酒店';
            order.dropoffAddress = '吉隆坡国际机场';
        }

        // 根据人数推荐车型
        if (!order.carType && order.passengerCount) {
            if (order.passengerCount <= 4) {
                order.carType = '经济';
                order.seatCount = 5;
            } else if (order.passengerCount <= 7) {
                order.carType = '舒适';
                order.seatCount = 7;
            } else {
                order.carType = '豪华';
                order.seatCount = 10;
            }
        }

        return order;
    }
}

// 导出解析器类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChongDealerOrderParser;
} else if (typeof window !== 'undefined') {
    window.ChongDealerOrderParser = ChongDealerOrderParser;
}
