/**
 * @file ChongDealerOrderParser.test.js - Chong Dealer订单解析器测试
 * @description 测试Chong Dealer订单解析器的各种订单格式
 */

const ChongDealerOrderParser = require('./ChongDealerOrderParser');

/**
 * @function testChongDealerParser - 测试Chong Dealer解析器
 */
function testChongDealerParser() {
    const parser = new ChongDealerOrderParser();
    
    console.log('🧪 开始测试 Chong Dealer 订单解析器...\n');
    
    // 测试用例1：标准详细格式
    testStandardFormat(parser);
    
    // 测试用例2：简化格式
    testSimplifiedFormat(parser);
    
    // 测试用例3：多程订单
    testMultiTripOrder(parser);
    
    // 测试用例4：特殊服务
    testSpecialServices(parser);
    
    // 测试用例5：实际聊天记录片段
    testRealChatFragment(parser);
    
    console.log('✅ 所有测试完成！');
}

/**
 * @function testStandardFormat - 测试标准格式
 * @param {ChongDealerOrderParser} parser - 解析器实例
 */
function testStandardFormat(parser) {
    console.log('📝 测试1：标准详细格式');
    
    const testText = `
接送机类型：接机
姓名：刘思雨
航班号：CA871
用车时间：3.2晚11点30
接客地址：吉隆坡机场T1
送客地址：One Residences @ Chan Sow Lin by Birdy Stay
乘客人数：4
行李数量：3
联系方式：15296989128
微信号：Rain14321
`;
    
    const orders = parser.parseOrders(testText);
    console.log('解析结果：', JSON.stringify(orders, null, 2));
    console.log(`✅ 解析到 ${orders.length} 个订单\n`);
}

/**
 * @function testSimplifiedFormat - 测试简化格式
 * @param {ChongDealerOrderParser} parser - 解析器实例
 */
function testSimplifiedFormat(parser) {
    console.log('📝 测试2：简化格式');
    
    const testText = `
3.3接机：D7321 19.45抵达 
3.5送机：OD1900 08.00起飞 

姓名：袁媛
人数：2
车型：经济五座
酒店：吉隆坡希尔顿逸林酒店
结算：75+75+30
`;
    
    const orders = parser.parseOrders(testText);
    console.log('解析结果：', JSON.stringify(orders, null, 2));
    console.log(`✅ 解析到 ${orders.length} 个订单\n`);
}

/**
 * @function testMultiTripOrder - 测试多程订单
 * @param {ChongDealerOrderParser} parser - 解析器实例
 */
function testMultiTripOrder(parser) {
    console.log('📝 测试3：多程订单');
    
    const testText = `
3月2日：吉隆坡接机
D7343, 08:50抵达

3月4日：吉隆坡送机
AK5740，18:15起飞，15：00接客人

客人：3人 周怡/于秋浩 15727366883 
车型：5座车
酒店：菲斯2期酒店
结算：75*2
`;
    
    const orders = parser.parseOrders(testText);
    console.log('解析结果：', JSON.stringify(orders, null, 2));
    console.log(`✅ 解析到 ${orders.length} 个订单\n`);
}

/**
 * @function testSpecialServices - 测试特殊服务
 * @param {ChongDealerOrderParser} parser - 解析器实例
 */
function testSpecialServices(parser) {
    console.log('📝 测试4：特殊服务');
    
    const testText = `
3月11日：吉隆坡接机+举牌
MH763  21:20抵达

姓名：CHEN KUNLIAN
人数：1人
车型：经济5座
酒店：8 Kia Peng Suites Residence
结算：75+30

机场转乘：吉隆坡T2换T1 点对点
接机航班: AK5741  3.2 00:20抵达 
送机航班: ZH8024  3.2  04：20 起飞 

客人姓名：杨晔
人数：3人
车型：经济五座
结算：30*1.2+20马币
`;
    
    const orders = parser.parseOrders(testText);
    console.log('解析结果：', JSON.stringify(orders, null, 2));
    console.log(`✅ 解析到 ${orders.length} 个订单\n`);
}

/**
 * @function testRealChatFragment - 测试实际聊天记录片段
 * @param {ChongDealerOrderParser} parser - 解析器实例
 */
function testRealChatFragment(parser) {
    console.log('📝 测试5：实际聊天记录片段');
    
    const testText = `
[2024/2/28 15:59:13] CHONG 车头: 用车地点：吉隆坡
用车时间：03月23日  17点，  3大人（7座）
客人姓名：黄子慧3人
航班号： CZ8301    广州白云T2 - 吉隆坡T1  12:25  17:00 
接机/送机：接机
接送地址：吉隆坡网红泳池吉隆坡市中心双子塔豪瑪酒店(The Platinum 2 Kuala Lumpur by Holma)
-------
用车时间：03月25日   3点半左右，3大人（7座）
客人姓名：黄子慧3人 
航班号：AK6224    吉隆坡 - 登嘉楼 07:30  08:30 
接机/送机：送机
接送地址：吉隆坡网红泳池吉隆坡市中心双子塔豪瑪酒店(The Platinum 2 Kuala Lumpur by Holma)

迹象
[2024/2/28 15:59:40] CHONG 车头: @60173268882 @60132661322 @60123779818 进单后 放个emoji

[2024/2/29 12:55:53] CHONG 车头: 赵小涵   1人 5座

吉隆坡接送机：

1. MF823   03月01日  厦门高崎 - 吉隆坡  20:05  00:10+1   
2. MF824   03月06日  吉隆坡 - 厦门高崎  08:00  12:05   
酒店：3.1-3.6吉隆坡斯特格酒店(STEG Kuala Lumpur)

迹象说 *取消*
`;
    
    const orders = parser.parseOrders(testText);
    console.log('解析结果：', JSON.stringify(orders, null, 2));
    console.log(`✅ 解析到 ${orders.length} 个订单\n`);
}

/**
 * @function runPerformanceTest - 运行性能测试
 * @param {ChongDealerOrderParser} parser - 解析器实例
 */
function runPerformanceTest(parser) {
    console.log('⚡ 性能测试：解析大量订单');
    
    const largeText = `
3.3接机：D7321 19.45抵达 
姓名：客人1
人数：2
车型：经济五座
-------
3.4接机：MH123 20.30抵达
姓名：客人2
人数：4
车型：舒适七座
-------
3.5送机：AK456 08.00起飞
姓名：客人3
人数：1
车型：经济五座
`.repeat(100); // 重复100次，模拟300个订单
    
    const startTime = Date.now();
    const orders = parser.parseOrders(largeText);
    const endTime = Date.now();
    
    console.log(`📊 解析 ${orders.length} 个订单耗时：${endTime - startTime}ms`);
    console.log(`📈 平均每个订单耗时：${((endTime - startTime) / orders.length).toFixed(2)}ms\n`);
}

// 运行测试
if (require.main === module) {
    testChongDealerParser();
    
    // 可选：运行性能测试
    // const parser = new ChongDealerOrderParser();
    // runPerformanceTest(parser);
}

module.exports = {
    testChongDealerParser,
    runPerformanceTest
};
