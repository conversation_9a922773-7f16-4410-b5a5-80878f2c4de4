/**
 * @file FallbackParser.js - 降级解析器
 * @description 当LLM解析失败时的基础解析备用方案
 */

class FallbackParser {
    constructor() {
        // 最基础的关键词匹配规则
        this.basicPatterns = {
            // 客人信息
            customerName: /(姓名|客人|客户)[:：]\s*([^\n\d]+)/i,
            phone: /1[3-9]\d{9}/,
            
            // 服务信息
            serviceType: /(接机|送机)/,
            flightNumber: /([A-Z]{1,3}\d{2,4})/,
            
            // 基础日期匹配
            datePattern: /(\d{1,2})[月\.](\d{1,2})/,
            
            // 人数
            passengerCount: /(\d+)(人|大人|位)/,
            
            // 特殊服务
            signService: /举牌/,
            transfer: /转乘|转站/,
            charter: /包车/
        };

        this.logger = null;
    }

    /**
     * @function initialize - 初始化降级解析器
     * @param {Object} dependencies - 依赖对象
     */
    initialize(dependencies) {
        this.logger = dependencies.logger;
        this.logger?.info('降级解析器已初始化');
    }

    /**
     * @function parseBasic - 基础解析方法
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型
     * @returns {Object} 解析结果
     */
    parseBasic(text, otaType) {
        try {
            this.logger?.info('开始降级解析');
            
            // 简单分段（按空行分割）
            const segments = text.split(/\n\s*\n/).filter(seg => seg.trim().length > 10);
            
            const orders = [];
            
            for (const segment of segments) {
                const order = this.parseSegment(segment, otaType);
                if (order) {
                    orders.push(order);
                }
            }

            this.logger?.info('降级解析完成', { orderCount: orders.length });

            return {
                success: true,
                orders,
                provider: 'fallback',
                confidence: 0.6 // 降级解析置信度较低
            };

        } catch (error) {
            this.logger?.error('降级解析失败', { error: error.message });
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * @function parseSegment - 解析单个段落
     * @param {string} segment - 文本段落
     * @param {string} otaType - OTA类型
     * @returns {Object|null} 订单对象
     */
    parseSegment(segment, otaType) {
        const order = {
            customerName: '',
            customerContact: '',
            serviceDate: '',
            serviceType: '',
            flightNumber: '',
            passengerCount: 1,
            pickupAddress: '',
            dropoffAddress: '',
            specialServices: [],
            originalText: segment,
            confidence: 0.6
        };

        // 提取客人姓名
        const nameMatch = segment.match(this.basicPatterns.customerName);
        if (nameMatch) {
            order.customerName = nameMatch[2].trim();
        }

        // 提取电话号码
        const phoneMatch = segment.match(this.basicPatterns.phone);
        if (phoneMatch) {
            order.customerContact = phoneMatch[0];
        }

        // 提取服务类型
        const serviceMatch = segment.match(this.basicPatterns.serviceType);
        if (serviceMatch) {
            order.serviceType = serviceMatch[1];
        }

        // 提取航班号
        const flightMatch = segment.match(this.basicPatterns.flightNumber);
        if (flightMatch) {
            order.flightNumber = flightMatch[1];
        }

        // 提取日期
        const dateMatch = segment.match(this.basicPatterns.datePattern);
        if (dateMatch) {
            const month = dateMatch[1].padStart(2, '0');
            const day = dateMatch[2].padStart(2, '0');
            order.serviceDate = `${month}月${day}日`;
        }

        // 提取人数
        const countMatch = segment.match(this.basicPatterns.passengerCount);
        if (countMatch) {
            order.passengerCount = parseInt(countMatch[1]);
        }

        // 提取特殊服务
        if (this.basicPatterns.signService.test(segment)) {
            order.specialServices.push('举牌');
        }
        if (this.basicPatterns.transfer.test(segment)) {
            order.specialServices.push('机场转乘');
        }
        if (this.basicPatterns.charter.test(segment)) {
            order.specialServices.push('包车');
        }

        // 基础地址推断
        if (order.serviceType === '接机') {
            order.pickupAddress = '吉隆坡国际机场';
            // 尝试提取酒店信息作为目的地
            const hotelMatch = segment.match(/(酒店|hotel)[:：]?\s*([^\n]+)/i);
            if (hotelMatch) {
                order.dropoffAddress = hotelMatch[2].trim();
            }
        } else if (order.serviceType === '送机') {
            order.dropoffAddress = '吉隆坡国际机场';
            // 尝试提取酒店信息作为出发地
            const hotelMatch = segment.match(/(酒店|hotel)[:：]?\s*([^\n]+)/i);
            if (hotelMatch) {
                order.pickupAddress = hotelMatch[2].trim();
            }
        }

        // 验证订单有效性
        if (this.isValidBasicOrder(order)) {
            return order;
        }

        return null;
    }

    /**
     * @function isValidBasicOrder - 验证基础订单有效性
     * @param {Object} order - 订单对象
     * @returns {boolean} 是否有效
     */
    isValidBasicOrder(order) {
        // 最低要求：有客人姓名或航班号，且有服务类型
        return (order.customerName || order.flightNumber) && order.serviceType;
    }

    /**
     * @function enhanceWithDefaults - 使用默认值增强订单
     * @param {Array} orders - 订单数组
     * @param {string} otaType - OTA类型
     * @returns {Array} 增强后的订单
     */
    enhanceWithDefaults(orders, otaType) {
        return orders.map(order => {
            const enhanced = { ...order };

            // 默认值填充
            if (!enhanced.customerContact && !enhanced.customerName) {
                enhanced.customerName = '未知客人';
            }

            if (!enhanced.serviceDate) {
                // 默认为明天
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                const month = (tomorrow.getMonth() + 1).toString().padStart(2, '0');
                const day = tomorrow.getDate().toString().padStart(2, '0');
                enhanced.serviceDate = `${month}月${day}日`;
            }

            if (!enhanced.pickupAddress && enhanced.serviceType === '接机') {
                enhanced.pickupAddress = '吉隆坡国际机场';
            }

            if (!enhanced.dropoffAddress && enhanced.serviceType === '送机') {
                enhanced.dropoffAddress = '吉隆坡国际机场';
            }

            // 添加降级标识
            enhanced.isFallbackParsed = true;
            enhanced.needsManualReview = true;

            return enhanced;
        });
    }

    /**
     * @function createEmergencyOrder - 创建紧急订单（最后手段）
     * @param {string} text - 原始文本
     * @param {string} otaType - OTA类型
     * @returns {Object} 紧急订单
     */
    createEmergencyOrder(text, otaType) {
        const now = new Date();
        const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
        
        return {
            customerName: '需要手动处理',
            customerContact: '',
            serviceDate: `${(tomorrow.getMonth() + 1).toString().padStart(2, '0')}月${tomorrow.getDate().toString().padStart(2, '0')}日`,
            serviceType: '接机', // 默认接机
            flightNumber: '',
            passengerCount: 1,
            pickupAddress: '吉隆坡国际机场',
            dropoffAddress: '市区酒店',
            specialServices: [],
            originalText: text,
            isEmergencyOrder: true,
            needsManualReview: true,
            confidence: 0.1,
            processingNote: '自动解析失败，需要人工处理'
        };
    }

    /**
     * @function getParsingStatistics - 获取解析统计
     * @param {Array} orders - 解析的订单
     * @returns {Object} 统计信息
     */
    getParsingStatistics(orders) {
        const stats = {
            totalOrders: orders.length,
            withCustomerName: 0,
            withFlightNumber: 0,
            withSpecialServices: 0,
            needsReview: 0,
            averageConfidence: 0
        };

        let totalConfidence = 0;

        orders.forEach(order => {
            if (order.customerName && order.customerName !== '需要手动处理') {
                stats.withCustomerName++;
            }
            if (order.flightNumber) {
                stats.withFlightNumber++;
            }
            if (order.specialServices && order.specialServices.length > 0) {
                stats.withSpecialServices++;
            }
            if (order.needsManualReview) {
                stats.needsReview++;
            }
            totalConfidence += order.confidence || 0;
        });

        stats.averageConfidence = orders.length > 0 ? 
            (totalConfidence / orders.length).toFixed(2) : 0;

        return stats;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FallbackParser;
} else if (typeof window !== 'undefined') {
    window.FallbackParser = FallbackParser;
}
