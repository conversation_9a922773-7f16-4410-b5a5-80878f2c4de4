/**
 * @file LLMProcessor.js - LLM处理引擎
 * @description DeepSeek主引擎 + Gemini备用的LLM处理系统
 */

class LLMProcessor {
    constructor() {
        this.deepSeekConfig = {
            apiEndpoint: 'https://api.deepseek.com/v1/chat/completions',
            model: 'deepseek-chat',
            timeout: 15000,
            maxRetries: 2
        };

        this.geminiConfig = {
            apiEndpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
            timeout: 30000,
            maxRetries: 1
        };

        this.promptTemplate = this.createPromptTemplate();
        this.outputSchema = this.createOutputSchema();
        this.logger = null;
    }

    /**
     * @function initialize - 初始化LLM处理器
     * @param {Object} config - 配置对象
     */
    initialize(config) {
        this.deepSeekConfig.apiKey = config.deepSeekApiKey;
        this.geminiConfig.apiKey = config.geminiApiKey;
        this.logger = config.logger;
        this.logger?.info('LLM处理器已初始化');
    }

    /**
     * @function parseWithLLM - 使用LLM解析订单
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型
     * @returns {Object} 解析结果
     */
    async parseWithLLM(text, otaType) {
        const startTime = Date.now();

        try {
            // 1. 首先尝试DeepSeek（15秒超时）
            this.logger?.info('开始DeepSeek解析');
            const deepSeekResult = await this.callDeepSeek(text, otaType);
            
            if (deepSeekResult.success) {
                this.logger?.info('DeepSeek解析成功', {
                    processingTime: Date.now() - startTime,
                    orderCount: deepSeekResult.orders.length
                });
                
                return {
                    success: true,
                    orders: deepSeekResult.orders,
                    provider: 'deepseek',
                    processingTime: Date.now() - startTime
                };
            }

            // 2. DeepSeek失败，尝试Gemini备用
            this.logger?.warn('DeepSeek解析失败，切换到Gemini', {
                deepSeekError: deepSeekResult.error
            });

            const geminiResult = await this.callGemini(text, otaType);
            
            if (geminiResult.success) {
                this.logger?.info('Gemini解析成功', {
                    processingTime: Date.now() - startTime,
                    orderCount: geminiResult.orders.length
                });
                
                return {
                    success: true,
                    orders: geminiResult.orders,
                    provider: 'gemini',
                    processingTime: Date.now() - startTime
                };
            }

            // 3. 两个LLM都失败
            this.logger?.error('所有LLM解析失败', {
                deepSeekError: deepSeekResult.error,
                geminiError: geminiResult.error
            });

            return {
                success: false,
                error: 'DeepSeek和Gemini解析均失败',
                processingTime: Date.now() - startTime
            };

        } catch (error) {
            this.logger?.error('LLM处理异常', { error: error.message });
            return {
                success: false,
                error: error.message,
                processingTime: Date.now() - startTime
            };
        }
    }

    /**
     * @function callDeepSeek - 调用DeepSeek API
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型
     * @returns {Object} 解析结果
     */
    async callDeepSeek(text, otaType) {
        try {
            const prompt = this.buildPrompt(text, otaType);
            
            const response = await this.makeApiCall(this.deepSeekConfig.apiEndpoint, {
                model: this.deepSeekConfig.model,
                messages: [
                    {
                        role: 'system',
                        content: this.promptTemplate.system
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 4000
            }, this.deepSeekConfig);

            return this.parseApiResponse(response, 'deepseek');

        } catch (error) {
            return {
                success: false,
                error: `DeepSeek API调用失败: ${error.message}`
            };
        }
    }

    /**
     * @function callGemini - 调用Gemini API
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型
     * @returns {Object} 解析结果
     */
    async callGemini(text, otaType) {
        try {
            const prompt = this.buildPrompt(text, otaType);
            
            const response = await this.makeApiCall(this.geminiConfig.apiEndpoint, {
                contents: [{
                    parts: [{
                        text: `${this.promptTemplate.system}\n\n${prompt}`
                    }]
                }],
                generationConfig: {
                    temperature: 0.1,
                    maxOutputTokens: 4000
                }
            }, this.geminiConfig);

            return this.parseApiResponse(response, 'gemini');

        } catch (error) {
            return {
                success: false,
                error: `Gemini API调用失败: ${error.message}`
            };
        }
    }

    /**
     * @function makeApiCall - 通用API调用方法
     * @param {string} endpoint - API端点
     * @param {Object} payload - 请求载荷
     * @param {Object} config - 配置
     * @returns {Object} API响应
     */
    async makeApiCall(endpoint, payload, config) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), config.timeout);

        try {
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${config.apiKey}`
                },
                body: JSON.stringify(payload),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
            }

            return await response.json();

        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error(`API调用超时 (${config.timeout}ms)`);
            }
            throw error;
        }
    }

    /**
     * @function parseApiResponse - 解析API响应
     * @param {Object} response - API响应
     * @param {string} provider - LLM提供商
     * @returns {Object} 解析结果
     */
    parseApiResponse(response, provider) {
        try {
            let content = '';

            if (provider === 'deepseek') {
                content = response.choices?.[0]?.message?.content || '';
            } else if (provider === 'gemini') {
                content = response.candidates?.[0]?.content?.parts?.[0]?.text || '';
            }

            if (!content) {
                return {
                    success: false,
                    error: '无法从API响应中提取内容'
                };
            }

            // 提取JSON内容
            const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/) || 
                             content.match(/\{[\s\S]*\}/);
            
            if (!jsonMatch) {
                return {
                    success: false,
                    error: '响应中未找到有效的JSON格式'
                };
            }

            const parsedData = JSON.parse(jsonMatch[1] || jsonMatch[0]);
            
            // 验证输出格式
            const validation = this.validateOutput(parsedData);
            if (!validation.isValid) {
                return {
                    success: false,
                    error: `输出格式验证失败: ${validation.errors.join(', ')}`
                };
            }

            return {
                success: true,
                orders: parsedData.orders || [],
                metadata: parsedData.metadata || {}
            };

        } catch (error) {
            return {
                success: false,
                error: `响应解析失败: ${error.message}`
            };
        }
    }

    /**
     * @function buildPrompt - 构建提示词
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型
     * @returns {string} 完整提示词
     */
    buildPrompt(text, otaType) {
        return `
请解析以下来自${otaType}平台的订单文本，提取所有订单信息并按照指定的JSON格式输出：

订单文本：
${text}

要求：
1. 识别所有订单（可能包含多个订单）
2. 提取每个订单的完整信息
3. 日期格式统一为中文格式（如：03月23日）
4. 服务类型使用中文（接机/送机）
5. 严格按照输出格式返回JSON

${this.promptTemplate.examples}
`;
    }

    /**
     * @function createPromptTemplate - 创建提示词模板
     * @returns {Object} 提示词模板
     */
    createPromptTemplate() {
        return {
            system: `你是一个专业的OTA订单解析助手。你的任务是从聊天记录中准确提取订单信息，并输出标准化的JSON格式。

核心要求：
1. 准确识别订单边界（多个订单用分隔符区分）
2. 提取所有关键字段信息
3. 处理中文日期格式
4. 识别特殊服务（举牌、机场转乘、包车等）
5. 输出必须是有效的JSON格式`,

            examples: `
输出格式示例：
\`\`\`json
{
  "orders": [
    {
      "customerName": "张三",
      "customerContact": "13800138000",
      "serviceDate": "03月23日",
      "serviceTime": "17点",
      "serviceType": "接机",
      "flightNumber": "CZ8301",
      "flightTime": "17:00",
      "passengerCount": 3,
      "pickupAddress": "吉隆坡国际机场T1",
      "dropoffAddress": "双子塔豪瑪酒店",
      "specialServices": ["举牌"],
      "originalText": "原始订单文本"
    }
  ],
  "metadata": {
    "totalOrders": 1,
    "processingNotes": "解析说明"
  }
}
\`\`\`
`
        };
    }

    /**
     * @function createOutputSchema - 创建输出格式验证模式
     * @returns {Object} 验证模式
     */
    createOutputSchema() {
        return {
            type: 'object',
            required: ['orders'],
            properties: {
                orders: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            customerName: { type: 'string' },
                            serviceType: { type: 'string' },
                            serviceDate: { type: 'string' }
                        }
                    }
                }
            }
        };
    }

    /**
     * @function validateOutput - 验证LLM输出格式
     * @param {Object} data - LLM输出数据
     * @returns {Object} 验证结果
     */
    validateOutput(data) {
        const errors = [];

        if (!data || typeof data !== 'object') {
            errors.push('输出必须是对象');
            return { isValid: false, errors };
        }

        if (!Array.isArray(data.orders)) {
            errors.push('orders字段必须是数组');
            return { isValid: false, errors };
        }

        // 验证每个订单
        data.orders.forEach((order, index) => {
            if (!order.customerName && !order.flightNumber) {
                errors.push(`订单${index + 1}缺少客人姓名或航班号`);
            }
        });

        return {
            isValid: errors.length === 0,
            errors
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LLMProcessor;
} else if (typeof window !== 'undefined') {
    window.LLMProcessor = LLMProcessor;
}
