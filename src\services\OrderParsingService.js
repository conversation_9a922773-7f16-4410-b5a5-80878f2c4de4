/**
 * @file OrderParsingService.js - 订单解析服务
 * @description 统一的订单解析服务，支持多种OTA平台格式
 */

const ChongDealerOrderParser = require('../parsers/ChongDealerOrderParser');

class OrderParsingService {
    constructor() {
        // 初始化各种解析器
        this.parsers = {
            chong_dealer: new ChongDealerOrderParser(),
            // 可以添加其他OTA解析器
            // gomyhire: new GoMyHireOrderParser(),
            // klook: new KlookOrderParser()
        };
        
        // OTA类型识别规则
        this.otaDetectionRules = [
            {
                type: 'chong_dealer',
                patterns: [
                    /CHONG 车头/i,
                    /用车地点[:：]/i,
                    /接送机类型[:：]/i,
                    /\*京鱼\*/,
                    /\*野马\*/,
                    /结算[:：].*?\d+/
                ]
            }
            // 可以添加其他OTA的识别规则
        ];
        
        this.logger = null; // 将在初始化时设置
    }

    /**
     * @function initialize - 初始化服务
     * @param {Object} dependencies - 依赖对象
     */
    initialize(dependencies) {
        this.logger = dependencies.logger;
        this.logger?.info('订单解析服务已初始化');
    }

    /**
     * @function parseOrderText - 解析订单文本
     * @param {string} text - 订单文本
     * @param {string} otaType - OTA类型（可选，自动检测）
     * @returns {Object} 解析结果
     */
    parseOrderText(text, otaType = null) {
        try {
            this.logger?.info('开始解析订单文本', { 
                textLength: text.length,
                specifiedOtaType: otaType 
            });

            // 1. 检测OTA类型
            const detectedOtaType = otaType || this.detectOtaType(text);
            
            if (!detectedOtaType) {
                this.logger?.warn('无法识别OTA类型');
                return {
                    success: false,
                    error: '无法识别OTA类型',
                    orders: []
                };
            }

            // 2. 获取对应的解析器
            const parser = this.parsers[detectedOtaType];
            if (!parser) {
                this.logger?.error('未找到对应的解析器', { otaType: detectedOtaType });
                return {
                    success: false,
                    error: `不支持的OTA类型: ${detectedOtaType}`,
                    orders: []
                };
            }

            // 3. 执行解析
            const orders = parser.parseOrders(text);
            
            this.logger?.info('订单解析完成', {
                otaType: detectedOtaType,
                orderCount: orders.length
            });

            return {
                success: true,
                otaType: detectedOtaType,
                orders: orders,
                metadata: {
                    parsedAt: new Date().toISOString(),
                    textLength: text.length,
                    orderCount: orders.length
                }
            };

        } catch (error) {
            this.logger?.error('订单解析失败', { error: error.message });
            return {
                success: false,
                error: error.message,
                orders: []
            };
        }
    }

    /**
     * @function detectOtaType - 检测OTA类型
     * @param {string} text - 订单文本
     * @returns {string|null} OTA类型
     */
    detectOtaType(text) {
        for (const rule of this.otaDetectionRules) {
            let matchCount = 0;
            
            for (const pattern of rule.patterns) {
                if (pattern.test(text)) {
                    matchCount++;
                }
            }
            
            // 如果匹配到一半以上的模式，认为是该OTA类型
            if (matchCount >= Math.ceil(rule.patterns.length / 2)) {
                this.logger?.info('检测到OTA类型', { 
                    type: rule.type, 
                    matchCount, 
                    totalPatterns: rule.patterns.length 
                });
                return rule.type;
            }
        }
        
        this.logger?.warn('未能检测到已知的OTA类型');
        return null;
    }

    /**
     * @function validateOrders - 验证订单数据
     * @param {Array} orders - 订单数组
     * @returns {Object} 验证结果
     */
    validateOrders(orders) {
        const validationResults = {
            valid: [],
            invalid: [],
            warnings: []
        };

        for (const order of orders) {
            const validation = this.validateSingleOrder(order);
            
            if (validation.isValid) {
                validationResults.valid.push(order);
            } else {
                validationResults.invalid.push({
                    order,
                    errors: validation.errors
                });
            }
            
            if (validation.warnings.length > 0) {
                validationResults.warnings.push({
                    order,
                    warnings: validation.warnings
                });
            }
        }

        return validationResults;
    }

    /**
     * @function validateSingleOrder - 验证单个订单
     * @param {Object} order - 订单对象
     * @returns {Object} 验证结果
     */
    validateSingleOrder(order) {
        const errors = [];
        const warnings = [];

        // 必填字段检查
        if (!order.customerName && !order.phone) {
            errors.push('缺少客人姓名或联系方式');
        }

        if (!order.serviceType) {
            errors.push('缺少服务类型（接机/送机）');
        }

        if (!order.serviceDate) {
            warnings.push('缺少服务日期');
        }

        // 逻辑检查
        if (order.passengerCount > order.seatCount) {
            warnings.push(`乘客人数(${order.passengerCount})超过车辆座位数(${order.seatCount})`);
        }

        if (order.serviceType === '接机' && !order.flightNumber) {
            warnings.push('接机服务缺少航班号');
        }

        if (order.serviceType === '送机' && !order.flightTime) {
            warnings.push('送机服务缺少航班时间');
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * @function getParsingStatistics - 获取解析统计信息
     * @returns {Object} 统计信息
     */
    getParsingStatistics() {
        // 这里可以添加统计逻辑，比如解析成功率、常见错误等
        return {
            supportedOtaTypes: Object.keys(this.parsers),
            totalParsers: Object.keys(this.parsers).length,
            detectionRules: this.otaDetectionRules.length
        };
    }

    /**
     * @function addParser - 添加新的解析器
     * @param {string} otaType - OTA类型
     * @param {Object} parser - 解析器实例
     * @param {Array} detectionPatterns - 检测模式
     */
    addParser(otaType, parser, detectionPatterns = []) {
        this.parsers[otaType] = parser;
        
        if (detectionPatterns.length > 0) {
            this.otaDetectionRules.push({
                type: otaType,
                patterns: detectionPatterns
            });
        }
        
        this.logger?.info('添加新的解析器', { otaType });
    }

    /**
     * @function processOrdersForGoMyHire - 为GoMyHire格式化订单
     * @param {Array} orders - 解析后的订单数组
     * @returns {Array} GoMyHire格式的订单数组
     */
    processOrdersForGoMyHire(orders) {
        return orders.map(order => ({
            // GoMyHire API所需的字段映射
            customerName: order.customerName,
            customerContact: order.phone || order.wechat,
            passengerCount: order.passengerCount,
            serviceType: order.serviceType === '接机' ? 'pickup' : 'dropoff',
            serviceDate: order.serviceDate,
            serviceTime: order.serviceTime || order.flightTime,
            flightNumber: order.flightNumber,
            pickupAddress: order.pickupAddress,
            dropoffAddress: order.dropoffAddress,
            carType: this.mapCarTypeToGoMyHire(order.carType, order.seatCount),
            specialRequirements: order.specialServices.join(', '),
            otaReference: order.id,
            originalOrder: order // 保留原始订单信息
        }));
    }

    /**
     * @function mapCarTypeToGoMyHire - 映射车型到GoMyHire格式
     * @param {string} carType - 原始车型
     * @param {number} seatCount - 座位数
     * @returns {string} GoMyHire车型
     */
    mapCarTypeToGoMyHire(carType, seatCount) {
        if (seatCount <= 5) {
            return 'Compact 5 Seater';
        } else if (seatCount <= 7) {
            return 'Comfort 7 Seater';
        } else {
            return 'Premium 10 Seater';
        }
    }
}

// 导出服务类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OrderParsingService;
} else if (typeof window !== 'undefined') {
    window.OrderParsingService = OrderParsingService;
}
