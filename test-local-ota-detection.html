<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地OTA识别测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-input {
            width: 100%;
            height: 120px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-family: monospace;
            margin-bottom: 10px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .result.success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .result.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .result.error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .feature-list {
            margin: 10px 0;
        }
        .feature-item {
            background: #e9ecef;
            padding: 5px 10px;
            margin: 5px 0;
            border-radius: 3px;
            font-size: 0.9em;
        }
        .score-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .score-item {
            background: #f1f3f4;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 本地OTA类型识别测试</h1>
        <p>测试新的本地OTA识别功能，不依赖LLM进行OTA类型判断</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>测试1: Chong Dealer 订单</h3>
            <textarea class="test-input" id="test1">
OTA：CHONG
航班：AK5747 到达时间：14:22
客人姓名：余欣怡
酒店：吉隆坡希尔顿酒店
接机时间：2025-02-14 14:22
联系邮箱：<EMAIL>
            </textarea>
            <button class="test-button" onclick="testOTADetection('test1', 'result1')">测试识别</button>
            <div id="result1" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>测试2: 携程订单</h3>
            <textarea class="test-input" id="test2">
携程订单号：1234567890123
客人姓名：张三
航班：CZ3456 起飞时间：16:30
酒店：Kuala Lumpur Hilton
送机时间：2025-02-15 13:00
来源：ctrip.com
            </textarea>
            <button class="test-button" onclick="testOTADetection('test2', 'result2')">测试识别</button>
            <div id="result2" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>测试3: 其他OTA平台</h3>
            <textarea class="test-input" id="test3">
Booking.com 预订确认
客人：John Smith
航班：MH370 到达：18:45
酒店：Grand Hyatt Kuala Lumpur
日期：2025-02-16
服务：机场接送
            </textarea>
            <button class="test-button" onclick="testOTADetection('test3', 'result3')">测试识别</button>
            <div id="result3" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>测试4: 通用订单</h3>
            <textarea class="test-input" id="test4">
客人姓名：李四
航班号：AA1234
到达时间：20:30
酒店：Mandarin Oriental
日期：2025-02-17
备注：需要接机服务
            </textarea>
            <button class="test-button" onclick="testOTADetection('test4', 'result4')">测试识别</button>
            <div id="result4" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>自定义测试</h3>
            <textarea class="test-input" id="custom" placeholder="在这里输入您的测试订单内容..."></textarea>
            <button class="test-button" onclick="testOTADetection('custom', 'customResult')">测试识别</button>
            <div id="customResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="logger.js"></script>
    <script src="app.js"></script>

    <script>
        // 初始化日志系统
        const logger = new Logger();
        
        // 创建LLM服务实例用于测试
        const llmService = new LLMService();

        /**
         * 测试OTA识别功能
         * @param {string} inputId - 输入框ID
         * @param {string} resultId - 结果显示区域ID
         */
        function testOTADetection(inputId, resultId) {
            const input = document.getElementById(inputId);
            const result = document.getElementById(resultId);
            const orderContent = input.value.trim();

            if (!orderContent) {
                showResult(resultId, {
                    ota_type: 'error',
                    confidence: 0,
                    reasoning: '请输入订单内容',
                    detected_features: []
                }, 'error');
                return;
            }

            try {
                // 调用本地OTA识别
                const detection = llmService.detectOTATypeLocally(orderContent);
                
                // 显示结果
                showResult(resultId, detection, getResultType(detection.confidence));
                
            } catch (error) {
                console.error('OTA识别测试失败:', error);
                showResult(resultId, {
                    ota_type: 'error',
                    confidence: 0,
                    reasoning: '识别过程出错: ' + error.message,
                    detected_features: []
                }, 'error');
            }
        }

        /**
         * 显示识别结果
         * @param {string} resultId - 结果显示区域ID
         * @param {Object} detection - 识别结果
         * @param {string} type - 结果类型
         */
        function showResult(resultId, detection, type) {
            const resultDiv = document.getElementById(resultId);
            
            let html = `
                <h4>识别结果</h4>
                <p><strong>OTA类型:</strong> ${detection.ota_type}</p>
                <p><strong>置信度:</strong> ${(detection.confidence * 100).toFixed(1)}%</p>
                <p><strong>识别理由:</strong> ${detection.reasoning}</p>
            `;

            if (detection.detected_features && detection.detected_features.length > 0) {
                html += `
                    <div class="feature-list">
                        <strong>检测到的特征:</strong>
                        ${detection.detected_features.map(feature => 
                            `<div class="feature-item">${feature}</div>`
                        ).join('')}
                    </div>
                `;
            }

            if (detection.score_breakdown) {
                html += `
                    <div class="score-breakdown">
                        <div class="score-item">
                            <strong>Chong Dealer</strong><br>
                            ${detection.score_breakdown.chong_dealer || 0} 分
                        </div>
                        <div class="score-item">
                            <strong>携程</strong><br>
                            ${detection.score_breakdown.ctrip || 0} 分
                        </div>
                        <div class="score-item">
                            <strong>其他OTA</strong><br>
                            ${detection.score_breakdown.other || 0} 分
                        </div>
                    </div>
                `;
            }

            if (detection.confidence_breakdown) {
                html += `
                    <div class="score-breakdown">
                        <div class="score-item">
                            <strong>Chong置信度</strong><br>
                            ${(detection.confidence_breakdown.chong_dealer * 100).toFixed(1)}%
                        </div>
                        <div class="score-item">
                            <strong>携程置信度</strong><br>
                            ${(detection.confidence_breakdown.ctrip * 100).toFixed(1)}%
                        </div>
                        <div class="score-item">
                            <strong>其他置信度</strong><br>
                            ${(detection.confidence_breakdown.other * 100).toFixed(1)}%
                        </div>
                    </div>
                `;
            }

            resultDiv.innerHTML = html;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        /**
         * 根据置信度确定结果类型
         * @param {number} confidence - 置信度
         * @returns {string} 结果类型
         */
        function getResultType(confidence) {
            if (confidence >= 0.7) return 'success';
            if (confidence >= 0.3) return 'warning';
            return 'error';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('本地OTA识别测试页面已加载');
            console.log('LLM服务实例:', llmService);
        });
    </script>
</body>
</html>
