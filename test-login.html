<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>登录功能测试</h1>
    
    <form id="testForm">
        <div class="form-group">
            <label for="email">邮箱:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="1234" required>
        </div>
        <button type="submit">测试登录</button>
    </form>
    
    <div id="result"></div>

    <script>
        // 配置
        const API_BASE_URL = 'https://staging.gomyhire.com.my/api';
        
        // 测试登录函数
        async function testLogin(email, password) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>正在测试登录...</p>';
            
            try {
                console.log('发起登录请求:', { email, url: `${API_BASE_URL}/login` });
                
                const response = await axios.post(`${API_BASE_URL}/login`, {
                    email: email,
                    password: password
                });
                
                console.log('登录响应:', response);
                
                if (response.data.status && response.data.token) {
                    const actualToken = response.data.token.split('|')[1] || response.data.token;
                    
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ 登录成功!</h3>
                            <p><strong>状态:</strong> ${response.data.status}</p>
                            <p><strong>Token:</strong> ${actualToken.substring(0, 20)}...</p>
                            <p><strong>用户信息:</strong> ${JSON.stringify(response.data.user || {}, null, 2)}</p>
                        </div>
                    `;
                    
                    // 测试获取后台用户
                    await testGetBackendUsers(actualToken);
                    
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 登录失败</h3>
                            <p>响应格式错误: ${JSON.stringify(response.data)}</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('登录错误:', error);
                
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 登录失败</h3>
                        <p><strong>错误信息:</strong> ${error.message}</p>
                        <p><strong>状态码:</strong> ${error.response?.status || '未知'}</p>
                        <p><strong>响应数据:</strong> ${JSON.stringify(error.response?.data || {}, null, 2)}</p>
                    </div>
                `;
            }
        }
        
        // 测试获取后台用户
        async function testGetBackendUsers(token) {
            try {
                console.log('测试获取后台用户...');
                
                const response = await axios.get(`${API_BASE_URL}/backend_users`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    params: { search: '' }
                });
                
                console.log('后台用户响应:', response);
                
                const users = response.data.data || [];
                
                document.getElementById('result').innerHTML += `
                    <div class="result success">
                        <h3>✅ 获取后台用户成功!</h3>
                        <p><strong>用户数量:</strong> ${users.length}</p>
                        <p><strong>用户列表:</strong></p>
                        <pre>${JSON.stringify(users, null, 2)}</pre>
                    </div>
                `;
                
            } catch (error) {
                console.error('获取后台用户错误:', error);
                
                document.getElementById('result').innerHTML += `
                    <div class="result error">
                        <h3>❌ 获取后台用户失败</h3>
                        <p><strong>错误信息:</strong> ${error.message}</p>
                        <p><strong>状态码:</strong> ${error.response?.status || '未知'}</p>
                        <p><strong>响应数据:</strong> ${JSON.stringify(error.response?.data || {}, null, 2)}</p>
                    </div>
                `;
            }
        }
        
        // 绑定表单提交事件
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            testLogin(email, password);
        });
        
        // 页面加载完成后自动测试
        window.addEventListener('load', function() {
            console.log('页面加载完成，开始自动测试登录...');
            testLogin('<EMAIL>', '1234');
        });
    </script>
</body>
</html>
