<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            min-width: 300px;
        }
        .hidden {
            display: none !important;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
            border: none;
        }
        button:hover {
            background: #0056b3;
        }
        .error-message {
            color: red;
            margin-top: 10px;
        }
        #console {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 10px;
            margin-top: 20px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>简单登录测试</h1>
    
    <!-- 登录窗口 -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <h2>系统登录</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">邮箱:</label>
                    <input type="email" id="email" value="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" value="1234" required>
                </div>
                <button type="submit">登录</button>
                <div id="loginError" class="error-message"></div>
            </form>
        </div>
    </div>

    <!-- 主界面 -->
    <div id="mainApp" class="hidden">
        <h2>✅ 登录成功！</h2>
        <p>欢迎使用OTA订单处理系统</p>
        <button onclick="logout()">退出登录</button>
    </div>

    <!-- 控制台输出 -->
    <div id="console"></div>

    <script>
        // 简单的日志函数
        function log(message, data = null) {
            const console = document.getElementById('console');
            const time = new Date().toLocaleTimeString();
            let logMessage = `[${time}] ${message}`;
            if (data) {
                logMessage += ': ' + JSON.stringify(data, null, 2);
            }
            console.innerHTML += logMessage + '\n';
            console.scrollTop = console.scrollHeight;
            console.log(message, data);
        }

        // 配置
        const API_BASE_URL = 'https://staging.gomyhire.com.my/api';
        let userToken = null;

        // 显示登录模态框
        function showLoginModal() {
            const modal = document.getElementById('loginModal');
            const mainApp = document.getElementById('mainApp');
            
            if (modal) {
                modal.style.display = 'block';
                log('显示登录模态框');
            }
            
            if (mainApp) {
                mainApp.classList.add('hidden');
                log('隐藏主界面');
            }
        }

        // 隐藏登录模态框
        function hideLoginModal() {
            const modal = document.getElementById('loginModal');
            const mainApp = document.getElementById('mainApp');
            
            if (modal) {
                modal.style.display = 'none';
                log('隐藏登录模态框');
            }
            
            if (mainApp) {
                mainApp.classList.remove('hidden');
                log('显示主界面');
            }
        }

        // 显示错误消息
        function showError(message) {
            const errorDiv = document.getElementById('loginError');
            if (errorDiv) {
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
            }
            log('显示错误消息', { message });
        }

        // 登录处理
        async function handleLogin(event) {
            event.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            log('开始用户登录', { email });

            try {
                const response = await axios.post(`${API_BASE_URL}/login`, {
                    email: email,
                    password: password
                });
                
                log('登录API响应', {
                    status: response.status,
                    data: response.data
                });
                
                if (response.data.status && response.data.token) {
                    // 提取实际token
                    const actualToken = response.data.token.split('|')[1] || response.data.token;
                    userToken = actualToken;
                    
                    log('登录成功', {
                        token: actualToken.substring(0, 20) + '...',
                        user: response.data.user
                    });
                    
                    hideLoginModal();
                    
                } else {
                    throw new Error('登录失败：无效的响应');
                }

            } catch (error) {
                log('登录失败', {
                    message: error.message,
                    response: error.response?.data,
                    status: error.response?.status
                });
                showError('登录失败: ' + error.message);
            }
        }

        // 退出登录
        function logout() {
            userToken = null;
            log('用户退出登录');
            showLoginModal();
        }

        // 初始化
        function initialize() {
            log('开始初始化应用');
            
            // 绑定登录表单事件
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', handleLogin);
                log('绑定登录表单事件');
            }
            
            // 显示登录模态框
            showLoginModal();
            
            log('应用初始化完成');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initialize);
    </script>
</body>
</html>
