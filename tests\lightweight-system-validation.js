/**
 * @file lightweight-system-validation.js - 轻量化系统验证测试
 * @description 基于_chat.txt真实数据验证重构后系统的性能和准确性
 */

const LightweightOrderProcessor = require('../src/services/LightweightOrderProcessor');
const LLMProcessor = require('../src/services/LLMProcessor');
const FallbackParser = require('../src/services/FallbackParser');
const fs = require('fs');
const path = require('path');

class LightweightSystemValidator {
    constructor() {
        this.processor = new LightweightOrderProcessor();
        this.llmProcessor = new LLMProcessor();
        this.fallbackParser = new FallbackParser();
        
        this.testResults = {
            totalTests: 0,
            passed: 0,
            failed: 0,
            performance: {},
            accuracy: {},
            errors: []
        };

        this.logger = {
            info: (msg, data) => console.log(`ℹ️  ${msg}`, data || ''),
            warn: (msg, data) => console.warn(`⚠️  ${msg}`, data || ''),
            error: (msg, data) => console.error(`❌ ${msg}`, data || '')
        };
    }

    /**
     * @function runFullValidation - 运行完整验证测试
     */
    async runFullValidation() {
        console.log('🚀 开始轻量化系统验证测试\n');

        try {
            // 初始化系统
            await this.initializeSystem();

            // 1. 基础功能测试
            await this.testBasicFunctionality();

            // 2. 真实数据测试
            await this.testWithRealChatData();

            // 3. 性能基准测试
            await this.testPerformanceBenchmarks();

            // 4. 日期格式验证
            await this.testDateFormatConversion();

            // 5. GoMyHire兼容性测试
            await this.testGoMyHireCompatibility();

            // 6. 错误处理测试
            await this.testErrorHandling();

            // 生成测试报告
            this.generateTestReport();

        } catch (error) {
            console.error('验证测试失败:', error);
        }
    }

    /**
     * @function initializeSystem - 初始化系统
     */
    async initializeSystem() {
        console.log('🔧 初始化系统组件...');

        // 模拟API密钥（实际使用时需要真实密钥）
        const mockConfig = {
            deepSeekApiKey: 'mock-deepseek-key',
            geminiApiKey: 'mock-gemini-key',
            logger: this.logger
        };

        this.llmProcessor.initialize(mockConfig);
        this.processor.initialize({
            llmProcessor: this.llmProcessor,
            logger: this.logger
        });
        this.fallbackParser.initialize({ logger: this.logger });

        console.log('✅ 系统初始化完成\n');
    }

    /**
     * @function testBasicFunctionality - 测试基础功能
     */
    async testBasicFunctionality() {
        console.log('📋 测试基础功能...');

        const testCases = [
            {
                name: 'OTA类型检测',
                text: 'CHONG 车头: 用车地点：吉隆坡',
                expectedOta: 'chong_dealer'
            },
            {
                name: '简单订单格式',
                text: `
                用车地点：吉隆坡
                用车时间：03月23日 17点，3大人（7座）
                客人姓名：张三
                航班号：CZ8301
                接机/送机：接机
                `,
                expectedOrders: 1
            }
        ];

        for (const testCase of testCases) {
            try {
                console.log(`  测试: ${testCase.name}`);
                
                if (testCase.expectedOta) {
                    const otaType = this.processor.detectOtaType(testCase.text);
                    if (otaType === testCase.expectedOta) {
                        console.log(`    ✅ 通过 - 检测到OTA类型: ${otaType}`);
                        this.testResults.passed++;
                    } else {
                        console.log(`    ❌ 失败 - 期望: ${testCase.expectedOta}, 实际: ${otaType}`);
                        this.testResults.failed++;
                    }
                }

                this.testResults.totalTests++;

            } catch (error) {
                console.log(`    ❌ 异常: ${error.message}`);
                this.testResults.failed++;
                this.testResults.errors.push({
                    test: testCase.name,
                    error: error.message
                });
            }
        }

        console.log('');
    }

    /**
     * @function testWithRealChatData - 使用真实聊天数据测试
     */
    async testWithRealChatData() {
        console.log('📄 使用真实聊天数据测试...');

        try {
            // 读取_chat.txt文件
            let chatContent = '';
            try {
                chatContent = fs.readFileSync(path.join(__dirname, '../_chat.txt'), 'utf8');
                console.log(`  📁 成功读取聊天记录: ${chatContent.length} 字符`);
            } catch (error) {
                console.log('  ⚠️  无法读取_chat.txt，使用模拟数据');
                chatContent = this.getMockChatData();
            }

            // 提取测试片段
            const testSegments = this.extractTestSegments(chatContent);
            console.log(`  📊 提取到 ${testSegments.length} 个测试片段`);

            let successCount = 0;
            let totalProcessingTime = 0;

            for (let i = 0; i < Math.min(testSegments.length, 5); i++) {
                const segment = testSegments[i];
                console.log(`  测试片段 ${i + 1}:`);

                try {
                    const startTime = Date.now();
                    
                    // 使用降级解析器测试（避免真实API调用）
                    const result = this.fallbackParser.parseBasic(segment, 'chong_dealer');
                    
                    const processingTime = Date.now() - startTime;
                    totalProcessingTime += processingTime;

                    if (result.success && result.orders.length > 0) {
                        console.log(`    ✅ 成功解析 ${result.orders.length} 个订单 (${processingTime}ms)`);
                        successCount++;
                        
                        // 显示第一个订单的关键信息
                        const order = result.orders[0];
                        console.log(`    📋 客人: ${order.customerName || '未知'}`);
                        console.log(`    📋 服务: ${order.serviceType || '未知'}`);
                        console.log(`    📋 日期: ${order.serviceDate || '未知'}`);
                    } else {
                        console.log(`    ❌ 解析失败: ${result.error || '未知错误'}`);
                    }

                } catch (error) {
                    console.log(`    ❌ 异常: ${error.message}`);
                }

                this.testResults.totalTests++;
            }

            // 计算统计信息
            const successRate = (successCount / Math.min(testSegments.length, 5)) * 100;
            const avgProcessingTime = totalProcessingTime / Math.min(testSegments.length, 5);

            console.log(`  📈 成功率: ${successRate.toFixed(1)}%`);
            console.log(`  ⏱️  平均处理时间: ${avgProcessingTime.toFixed(0)}ms`);

            this.testResults.accuracy.realDataSuccessRate = successRate;
            this.testResults.performance.avgProcessingTime = avgProcessingTime;

            this.testResults.passed += successCount;
            this.testResults.failed += (Math.min(testSegments.length, 5) - successCount);

        } catch (error) {
            console.log(`  ❌ 真实数据测试失败: ${error.message}`);
            this.testResults.errors.push({
                test: '真实数据测试',
                error: error.message
            });
        }

        console.log('');
    }

    /**
     * @function testPerformanceBenchmarks - 性能基准测试
     */
    async testPerformanceBenchmarks() {
        console.log('⚡ 性能基准测试...');

        const benchmarks = {
            otaDetection: { target: 100, actual: 0 },
            fallbackParsing: { target: 5000, actual: 0 },
            dateConversion: { target: 10, actual: 0 }
        };

        // OTA检测性能
        const otaTestText = 'CHONG 车头: 用车地点：吉隆坡 *京鱼*';
        const otaStartTime = Date.now();
        for (let i = 0; i < 100; i++) {
            this.processor.detectOtaType(otaTestText);
        }
        benchmarks.otaDetection.actual = (Date.now() - otaStartTime) / 100;

        // 降级解析性能
        const parseTestText = `
        用车地点：吉隆坡
        客人姓名：测试客人
        航班号：CZ8301
        接机/送机：接机
        `;
        const parseStartTime = Date.now();
        this.fallbackParser.parseBasic(parseTestText, 'chong_dealer');
        benchmarks.fallbackParsing.actual = Date.now() - parseStartTime;

        // 日期转换性能
        const dateStartTime = Date.now();
        for (let i = 0; i < 1000; i++) {
            this.processor.formatDateForGoMyHire('03月23日');
        }
        benchmarks.dateConversion.actual = (Date.now() - dateStartTime) / 1000;

        // 输出结果
        for (const [test, benchmark] of Object.entries(benchmarks)) {
            const status = benchmark.actual <= benchmark.target ? '✅' : '❌';
            console.log(`  ${status} ${test}: ${benchmark.actual.toFixed(2)}ms (目标: ${benchmark.target}ms)`);
        }

        this.testResults.performance = benchmarks;
        console.log('');
    }

    /**
     * @function testDateFormatConversion - 测试日期格式转换
     */
    async testDateFormatConversion() {
        console.log('📅 日期格式转换测试...');

        const dateTests = [
            { input: '03月23日', expected: '23-03-2024' },
            { input: '3.23', expected: '23-03-2024' },
            { input: '12月31日', expected: '31-12-2024' },
            { input: '1.1', expected: '01-01-2024' }
        ];

        let passedTests = 0;

        for (const test of dateTests) {
            const result = this.processor.formatDateForGoMyHire(test.input);
            const currentYear = new Date().getFullYear();
            const expectedWithCurrentYear = test.expected.replace('2024', currentYear.toString());
            
            if (result === expectedWithCurrentYear) {
                console.log(`  ✅ ${test.input} → ${result}`);
                passedTests++;
            } else {
                console.log(`  ❌ ${test.input} → ${result} (期望: ${expectedWithCurrentYear})`);
            }
        }

        console.log(`  📊 日期转换成功率: ${(passedTests / dateTests.length * 100).toFixed(1)}%`);
        this.testResults.accuracy.dateConversionRate = (passedTests / dateTests.length) * 100;
        console.log('');
    }

    /**
     * @function testGoMyHireCompatibility - GoMyHire兼容性测试
     */
    async testGoMyHireCompatibility() {
        console.log('🔗 GoMyHire兼容性测试...');

        const mockOrder = {
            customerName: '张三',
            customerContact: '13800138000',
            serviceDate: '03月23日',
            serviceType: '接机',
            passengerCount: 3,
            carTypeId: 1,
            backendUserId: 2,
            subCategoryId: 1
        };

        try {
            const goMyHireOrder = this.processor.convertToGoMyHireFormat([mockOrder])[0];
            const validation = this.processor.validateGoMyHireOrder(goMyHireOrder);

            console.log('  📋 转换后的GoMyHire格式:');
            console.log(`    customer_name: ${goMyHireOrder.customer_name}`);
            console.log(`    service_date: ${goMyHireOrder.service_date}`);
            console.log(`    service_type: ${goMyHireOrder.service_type}`);
            console.log(`    car_type_id: ${goMyHireOrder.car_type_id}`);

            if (validation.isValid) {
                console.log('  ✅ GoMyHire格式验证通过');
                this.testResults.passed++;
            } else {
                console.log('  ❌ GoMyHire格式验证失败:', validation.errors);
                this.testResults.failed++;
            }

        } catch (error) {
            console.log(`  ❌ GoMyHire兼容性测试失败: ${error.message}`);
            this.testResults.failed++;
        }

        this.testResults.totalTests++;
        console.log('');
    }

    /**
     * @function testErrorHandling - 错误处理测试
     */
    async testErrorHandling() {
        console.log('🛡️  错误处理测试...');

        const errorTests = [
            { name: '空文本', text: '' },
            { name: '无效格式', text: 'random text without order info' },
            { name: '特殊字符', text: '!@#$%^&*()' }
        ];

        for (const test of errorTests) {
            try {
                console.log(`  测试: ${test.name}`);
                const result = this.fallbackParser.parseBasic(test.text, 'chong_dealer');
                
                if (result.success || result.error) {
                    console.log('    ✅ 错误处理正常');
                    this.testResults.passed++;
                } else {
                    console.log('    ❌ 错误处理异常');
                    this.testResults.failed++;
                }

            } catch (error) {
                console.log(`    ✅ 捕获异常: ${error.message}`);
                this.testResults.passed++;
            }

            this.testResults.totalTests++;
        }

        console.log('');
    }

    /**
     * @function extractTestSegments - 从聊天记录中提取测试片段
     * @param {string} chatContent - 聊天内容
     * @returns {Array} 测试片段数组
     */
    extractTestSegments(chatContent) {
        // 简单分割策略：按空行分割，过滤短片段
        return chatContent
            .split(/\n\s*\n/)
            .filter(segment => segment.trim().length > 50)
            .slice(0, 10); // 限制测试片段数量
    }

    /**
     * @function getMockChatData - 获取模拟聊天数据
     * @returns {string} 模拟数据
     */
    getMockChatData() {
        return `
CHONG 车头: 用车地点：吉隆坡
用车时间：03月23日 17点，3大人（7座）
客人姓名：张三
航班号：CZ8301
接机/送机：接机

*京鱼*

3.24接机：D7321 19.45抵达
姓名：李四
人数：2
车型：经济五座
酒店：吉隆坡希尔顿酒店

*野马*
        `;
    }

    /**
     * @function generateTestReport - 生成测试报告
     */
    generateTestReport() {
        console.log('📊 测试报告');
        console.log('=' .repeat(50));
        
        const successRate = this.testResults.totalTests > 0 ? 
            (this.testResults.passed / this.testResults.totalTests * 100).toFixed(1) : 0;

        console.log(`总测试数: ${this.testResults.totalTests}`);
        console.log(`通过: ${this.testResults.passed}`);
        console.log(`失败: ${this.testResults.failed}`);
        console.log(`成功率: ${successRate}%`);
        
        if (this.testResults.performance.avgProcessingTime) {
            console.log(`平均处理时间: ${this.testResults.performance.avgProcessingTime.toFixed(0)}ms`);
        }

        if (this.testResults.accuracy.dateConversionRate) {
            console.log(`日期转换准确率: ${this.testResults.accuracy.dateConversionRate.toFixed(1)}%`);
        }

        if (this.testResults.errors.length > 0) {
            console.log('\n❌ 错误详情:');
            this.testResults.errors.forEach(error => {
                console.log(`  - ${error.test}: ${error.error}`);
            });
        }

        console.log('\n✅ 验证测试完成！');
    }
}

// 运行验证测试
if (require.main === module) {
    const validator = new LightweightSystemValidator();
    validator.runFullValidation().catch(console.error);
}

module.exports = LightweightSystemValidator;
